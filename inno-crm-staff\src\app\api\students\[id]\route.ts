import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/database'
import { Role } from '@prisma/client'
import { z } from 'zod'

const updateStudentSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  phone: z.string().min(1, 'Phone is required').optional(),
  level: z.enum(['A1', 'A2', 'B1', 'B2', 'IELTS', 'SAT', 'MATH', 'KIDS']).optional(),
  branch: z.string().min(1, 'Branch is required').optional(),
  emergencyContact: z.string().optional(),
  status: z.enum(['ACTIVE', 'DROPPED', 'PAUSED', 'COMPLETED']).optional(),
  currentGroupId: z.string().optional(),
})

// GET /api/students/[id] - Get student by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions)
  
  if (!session || !['ADMIN', 'MANAGER', 'ACADEMIC_MANAGER', 'TEACHER'].includes(session.user.role)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  try {
    const student = await prisma.studentReference.findUnique({
      where: { id: params.id },
      include: {
        currentGroup: {
          select: {
            name: true,
            course: {
              select: {
                name: true,
                level: true,
              }
            },
            teacher: {
              select: {
                user: {
                  select: {
                    name: true,
                  }
                }
              }
            }
          }
        },
        paymentOverview: {
          orderBy: { createdAt: 'desc' },
          take: 10,
        }
      }
    })

    if (!student) {
      return NextResponse.json({ error: 'Student not found' }, { status: 404 })
    }

    return NextResponse.json(student)
  } catch (error) {
    console.error('Error fetching student:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PUT /api/students/[id] - Update student
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions)
  
  if (!session || !['ADMIN', 'MANAGER'].includes(session.user.role)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  try {
    const body = await request.json()
    const validatedData = updateStudentSchema.parse(body)

    // Check if student exists
    const existingStudent = await prisma.studentReference.findUnique({
      where: { id: params.id }
    })

    if (!existingStudent) {
      return NextResponse.json({ error: 'Student not found' }, { status: 404 })
    }

    // If phone is being updated, check for duplicates
    if (validatedData.phone && validatedData.phone !== existingStudent.phone) {
      const phoneExists = await prisma.studentReference.findUnique({
        where: { phone: validatedData.phone }
      })

      if (phoneExists) {
        return NextResponse.json(
          { error: 'Student with this phone number already exists' },
          { status: 400 }
        )
      }
    }

    // Update student
    const updatedStudent = await prisma.studentReference.update({
      where: { id: params.id },
      data: {
        ...validatedData,
        updatedAt: new Date(),
      },
      include: {
        currentGroup: {
          select: {
            name: true,
          }
        }
      }
    })

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: session.user.id,
        userRole: session.user.role as Role,
        action: 'UPDATE',
        resource: 'student',
        resourceId: params.id,
        details: {
          studentName: updatedStudent.name,
          changes: validatedData,
        }
      }
    })

    return NextResponse.json(updatedStudent)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }
    
    console.error('Error updating student:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// DELETE /api/students/[id] - Delete student
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions)
  
  if (!session || session.user.role !== 'ADMIN') {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  try {
    // Check if student exists
    const existingStudent = await prisma.studentReference.findUnique({
      where: { id: params.id }
    })

    if (!existingStudent) {
      return NextResponse.json({ error: 'Student not found' }, { status: 404 })
    }

    // Delete student (this will cascade to related records)
    await prisma.studentReference.delete({
      where: { id: params.id }
    })

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: session.user.id,
        userRole: session.user.role as Role,
        action: 'DELETE',
        resource: 'student',
        resourceId: params.id,
        details: {
          studentName: existingStudent.name,
          phone: existingStudent.phone,
        }
      }
    })

    return NextResponse.json({ message: 'Student deleted successfully' })
  } catch (error) {
    console.error('Error deleting student:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
