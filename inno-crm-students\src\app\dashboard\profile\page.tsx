import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { redirect } from 'next/navigation'
import { prisma } from '@/lib/database'
import { User, Phone, Mail, MapPin, Calendar, BookOpen } from 'lucide-react'
import Image from 'next/image'

export default async function ProfilePage() {
  const session = await getServerSession(authOptions)

  if (!session) {
    redirect('/auth/signin')
  }

  // Fetch student profile data
  const student = await prisma.student.findUnique({
    where: { userId: session.user.id },
    include: {
      user: true,
      currentGroupReference: {
        include: {
          teacherReference: true,
        }
      }
    }
  })

  if (!student) {
    redirect('/auth/signin')
  }

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-base-content">
          My Profile
        </h1>
        <p className="text-base-content/70 mt-2">
          View and manage your personal information
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Profile Card */}
        <div className="lg:col-span-1">
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body text-center">
              <div className="avatar placeholder mb-4">
                <div className="bg-neutral text-neutral-content rounded-full w-24 h-24">
                  {student.photoUrl ? (
                    <Image
                      src={student.photoUrl}
                      alt={student.user.name}
                      width={96}
                      height={96}
                      className="rounded-full"
                    />
                  ) : (
                    <span className="text-3xl">
                      {student.user.name.charAt(0)}
                    </span>
                  )}
                </div>
              </div>
              <h2 className="card-title justify-center">{student.user.name}</h2>
              <p className="text-base-content/70">Student</p>
              <div className="badge badge-primary mt-2">
                Level {student.level}
              </div>
              <div className="badge badge-outline mt-1">
                {student.status}
              </div>
            </div>
          </div>
        </div>

        {/* Personal Information */}
        <div className="lg:col-span-2">
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h2 className="card-title mb-4">Personal Information</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="form-control">
                  <label className="label">
                    <span className="label-text">Full Name</span>
                  </label>
                  <div className="flex items-center space-x-2">
                    <User className="h-4 w-4 text-base-content/50" />
                    <span>{student.user.name}</span>
                  </div>
                </div>

                <div className="form-control">
                  <label className="label">
                    <span className="label-text">Phone Number</span>
                  </label>
                  <div className="flex items-center space-x-2">
                    <Phone className="h-4 w-4 text-base-content/50" />
                    <span>{student.user.phone}</span>
                  </div>
                </div>

                {student.user.email && (
                  <div className="form-control">
                    <label className="label">
                      <span className="label-text">Email</span>
                    </label>
                    <div className="flex items-center space-x-2">
                      <Mail className="h-4 w-4 text-base-content/50" />
                      <span>{student.user.email}</span>
                    </div>
                  </div>
                )}

                <div className="form-control">
                  <label className="label">
                    <span className="label-text">Branch</span>
                  </label>
                  <div className="flex items-center space-x-2">
                    <MapPin className="h-4 w-4 text-base-content/50" />
                    <span>{student.branch}</span>
                  </div>
                </div>

                {student.dateOfBirth && (
                  <div className="form-control">
                    <label className="label">
                      <span className="label-text">Date of Birth</span>
                    </label>
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-base-content/50" />
                      <span>{new Date(student.dateOfBirth).toLocaleDateString()}</span>
                    </div>
                  </div>
                )}

                <div className="form-control">
                  <label className="label">
                    <span className="label-text">Current Level</span>
                  </label>
                  <div className="flex items-center space-x-2">
                    <BookOpen className="h-4 w-4 text-base-content/50" />
                    <span>{student.level}</span>
                  </div>
                </div>

                {student.emergencyContact && (
                  <div className="form-control md:col-span-2">
                    <label className="label">
                      <span className="label-text">Emergency Contact</span>
                    </label>
                    <div className="flex items-center space-x-2">
                      <Phone className="h-4 w-4 text-base-content/50" />
                      <span>{student.emergencyContact}</span>
                    </div>
                  </div>
                )}

                {student.address && (
                  <div className="form-control md:col-span-2">
                    <label className="label">
                      <span className="label-text">Address</span>
                    </label>
                    <div className="flex items-center space-x-2">
                      <MapPin className="h-4 w-4 text-base-content/50" />
                      <span>{student.address}</span>
                    </div>
                  </div>
                )}
              </div>

              <div className="card-actions justify-end mt-6">
                <button className="btn btn-primary">Edit Profile</button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Current Group Information */}
      {student.currentGroupReference && (
        <div className="mt-6">
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h2 className="card-title">Current Group</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label className="label">
                    <span className="label-text">Group Name</span>
                  </label>
                  <p className="font-medium">{student.currentGroupReference.name}</p>
                </div>
                <div>
                  <label className="label">
                    <span className="label-text">Course</span>
                  </label>
                  <p className="font-medium">{student.currentGroupReference.courseName}</p>
                </div>
                <div>
                  <label className="label">
                    <span className="label-text">Teacher</span>
                  </label>
                  <p className="font-medium">{student.currentGroupReference.teacherReference.name}</p>
                </div>
                <div>
                  <label className="label">
                    <span className="label-text">Room</span>
                  </label>
                  <p className="font-medium">{student.currentGroupReference.room || 'TBA'}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
