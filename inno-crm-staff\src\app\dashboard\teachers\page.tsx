import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { redirect } from 'next/navigation'
import { prisma } from '@/lib/database'
import { Users, Plus, Search, Star } from 'lucide-react'
import Image from 'next/image'

export default async function TeachersPage() {
  const session = await getServerSession(authOptions)

  if (!session) {
    redirect('/auth/signin')
  }

  // Check if user has permission to view teachers
  if (!['ADMIN', 'MANAGER', 'ACADEMIC_MANAGER'].includes(session.user.role)) {
    redirect('/dashboard')
  }

  // Fetch teachers data
  const teachers = await prisma.teacher.findMany({
    include: {
      user: {
        select: {
          name: true,
          phone: true,
          email: true,
        }
      },
      groups: {
        select: {
          name: true,
        }
      }
    },
    orderBy: { createdAt: 'desc' }
  })

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'A_LEVEL': return 'badge-success'
      case 'B_LEVEL': return 'badge-info'
      case 'C_LEVEL': return 'badge-warning'
      case 'NEW': return 'badge-ghost'
      default: return 'badge-ghost'
    }
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-base-content">
            Teacher Management
          </h1>
          <p className="text-base-content/70 mt-2">
            Manage teacher profiles and assignments
          </p>
        </div>
        <button className="btn btn-primary">
          <Plus className="h-4 w-4" />
          Add Teacher
        </button>
      </div>

      {/* Search and filters */}
      <div className="card bg-base-100 shadow-xl mb-6">
        <div className="card-body">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="form-control flex-1">
              <div className="input-group">
                <input
                  type="text"
                  placeholder="Search teachers..."
                  className="input input-bordered flex-1"
                />
                <button className="btn btn-square">
                  <Search className="h-4 w-4" />
                </button>
              </div>
            </div>
            <select className="select select-bordered">
              <option value="">All Subjects</option>
              <option value="English">English</option>
              <option value="Math">Math</option>
              <option value="IELTS">IELTS</option>
              <option value="SAT">SAT</option>
            </select>
            <select className="select select-bordered">
              <option value="">All Tiers</option>
              <option value="A_LEVEL">A Level</option>
              <option value="B_LEVEL">B Level</option>
              <option value="C_LEVEL">C Level</option>
              <option value="NEW">New</option>
            </select>
          </div>
        </div>
      </div>

      {/* Teachers table */}
      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <div className="overflow-x-auto">
            <table className="table table-zebra w-full">
              <thead>
                <tr>
                  <th>Teacher</th>
                  <th>Subject</th>
                  <th>Experience</th>
                  <th>Tier</th>
                  <th>Groups</th>
                  <th>Branch</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {teachers.length > 0 ? (
                  teachers.map((teacher) => (
                    <tr key={teacher.id}>
                      <td>
                        <div className="flex items-center space-x-3">
                          <div className="avatar placeholder">
                            <div className="bg-neutral text-neutral-content rounded-full w-10 h-10">
                              {teacher.photoUrl ? (
                                <Image
                                  src={teacher.photoUrl}
                                  alt={teacher.user.name}
                                  width={40}
                                  height={40}
                                  className="rounded-full"
                                />
                              ) : (
                                <span className="text-sm">
                                  {teacher.user.name.charAt(0)}
                                </span>
                              )}
                            </div>
                          </div>
                          <div>
                            <div className="font-bold">{teacher.user.name}</div>
                            <div className="text-sm text-base-content/70">
                              {teacher.user.phone}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td>
                        <span className="badge badge-outline">
                          {teacher.subject}
                        </span>
                      </td>
                      <td>
                        {teacher.experience ? (
                          <div className="flex items-center space-x-1">
                            <Star className="h-4 w-4 text-yellow-500" />
                            <span>{teacher.experience} years</span>
                          </div>
                        ) : (
                          'N/A'
                        )}
                      </td>
                      <td>
                        <span className={`badge ${getTierColor(teacher.tier)}`}>
                          {teacher.tier.replace('_', ' ')}
                        </span>
                      </td>
                      <td>
                        <span className="badge badge-neutral">
                          {teacher.groups.length} groups
                        </span>
                      </td>
                      <td>{teacher.branch}</td>
                      <td>
                        <div className="flex space-x-2">
                          <button className="btn btn-ghost btn-xs">View</button>
                          <button className="btn btn-ghost btn-xs">Edit</button>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={7} className="text-center py-8">
                      <div className="flex flex-col items-center space-y-2">
                        <Users className="h-12 w-12 text-base-content/30" />
                        <p className="text-base-content/70">No teachers found</p>
                        <button className="btn btn-primary btn-sm">
                          <Plus className="h-4 w-4" />
                          Add First Teacher
                        </button>
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )
}
