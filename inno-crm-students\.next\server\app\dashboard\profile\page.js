(()=>{var e={};e.id=399,e.ids=[399],e.modules={1322:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:s,blurHeight:n,blurDataURL:i,objectFit:a}=e,l=s?40*s:t,o=n?40*n:r,d=l&&o?"viewBox='0 0 "+l+" "+o+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+d+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(d?"none":"contain"===a?"xMidYMid":"cover"===a?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+i+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},1933:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:s,width:n,quality:i}=e,a=i||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(s)+"&w="+n+"&q="+a+(s.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}}),r.__next_img_default=!0;let s=r},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4631:(e,t,r)=>{"use strict";r.d(t,{StudentSidebar:()=>v});var s=r(60687),n=r(85814),i=r.n(n),a=r(16189),l=r(82136),o=r(49384),d=r(82348),c=r(53411),u=r(40228),m=r(86561),h=r(82080),p=r(85778),f=r(58887),x=r(58869),b=r(40083);let g=[{name:"Dashboard",href:"/dashboard",icon:c.A},{name:"My Classes",href:"/dashboard/classes",icon:u.A},{name:"Assessments",href:"/dashboard/assessments",icon:m.A},{name:"Progress",href:"/dashboard/progress",icon:h.A},{name:"Payments",href:"/dashboard/payments",icon:p.A},{name:"Messages",href:"/dashboard/messages",icon:f.A},{name:"Profile",href:"/dashboard/profile",icon:x.A}];function v(){let e=(0,a.usePathname)(),{data:t}=(0,l.useSession)();return(0,s.jsxs)("div",{className:"drawer-side",children:[(0,s.jsx)("label",{htmlFor:"drawer-toggle",className:"drawer-overlay"}),(0,s.jsxs)("aside",{className:"min-h-full w-64 bg-base-200 flex flex-col",children:[(0,s.jsx)("div",{className:"p-4",children:(0,s.jsx)("h1",{className:"text-xl font-bold text-primary",children:"Student Portal"})}),(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)("ul",{className:"menu p-4 space-y-2",children:g.map(t=>{let r=t.icon,n=e===t.href;return(0,s.jsx)("li",{children:(0,s.jsxs)(i(),{href:t.href,className:function(...e){return(0,d.QP)((0,o.$)(e))}("flex items-center space-x-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors",n?"bg-primary text-primary-content":"text-base-content hover:bg-base-300"),children:[(0,s.jsx)(r,{className:"h-5 w-5"}),(0,s.jsx)("span",{children:t.name})]})},t.name)})})}),t&&(0,s.jsxs)("div",{className:"p-4 border-t border-base-300",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,s.jsx)("div",{className:"avatar placeholder",children:(0,s.jsx)("div",{className:"bg-neutral text-neutral-content rounded-full w-10",children:(0,s.jsx)(x.A,{className:"h-5 w-5"})})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-base-content truncate",children:t.user.name}),(0,s.jsx)("p",{className:"text-xs text-base-content/70 truncate",children:"Student"})]})]}),(0,s.jsxs)("button",{onClick:()=>{(0,l.signOut)({callbackUrl:"/auth/signin"})},className:"btn btn-ghost btn-sm w-full justify-start",children:[(0,s.jsx)(b.A,{className:"h-4 w-4"}),"Sign Out"]})]})]})]})}},9131:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return o}}),r(21122);let s=r(1322),n=r(27894),i=["-moz-initial","fill","none","scale-down",void 0];function a(e){return void 0!==e.default}function l(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function o(e,t){var r,o;let d,c,u,{src:m,sizes:h,unoptimized:p=!1,priority:f=!1,loading:x,className:b,quality:g,width:v,height:j,fill:y=!1,style:w,overrideSrc:N,onLoad:P,onLoadingComplete:_,placeholder:S="empty",blurDataURL:C,fetchPriority:E,decoding:O="async",layout:R,objectFit:M,objectPosition:A,lazyBoundary:k,lazyRoot:z,...I}=e,{imgConf:D,showAltText:q,blurComplete:T,defaultLoader:G}=t,U=D||n.imageConfigDefault;if("allSizes"in U)d=U;else{let e=[...U.deviceSizes,...U.imageSizes].sort((e,t)=>e-t),t=U.deviceSizes.sort((e,t)=>e-t),s=null==(r=U.qualities)?void 0:r.sort((e,t)=>e-t);d={...U,allSizes:e,deviceSizes:t,qualities:s}}if(void 0===G)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let F=I.loader||G;delete I.loader,delete I.srcSet;let L="__next_img_default"in F;if(L){if("custom"===d.loader)throw Object.defineProperty(Error('Image with src "'+m+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=F;F=t=>{let{config:r,...s}=t;return e(s)}}if(R){"fill"===R&&(y=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[R];e&&(w={...w,...e});let t={responsive:"100vw",fill:"100vw"}[R];t&&!h&&(h=t)}let W="",B=l(v),X=l(j);if((o=m)&&"object"==typeof o&&(a(o)||void 0!==o.src)){let e=a(m)?m.default:m;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(c=e.blurWidth,u=e.blurHeight,C=C||e.blurDataURL,W=e.src,!y)if(B||X){if(B&&!X){let t=B/e.width;X=Math.round(e.height*t)}else if(!B&&X){let t=X/e.height;B=Math.round(e.width*t)}}else B=e.width,X=e.height}let V=!f&&("lazy"===x||void 0===x);(!(m="string"==typeof m?m:W)||m.startsWith("data:")||m.startsWith("blob:"))&&(p=!0,V=!1),d.unoptimized&&(p=!0),L&&!d.dangerouslyAllowSVG&&m.split("?",1)[0].endsWith(".svg")&&(p=!0);let H=l(g),$=Object.assign(y?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:M,objectPosition:A}:{},q?{}:{color:"transparent"},w),J=T||"empty"===S?null:"blur"===S?'url("data:image/svg+xml;charset=utf-8,'+(0,s.getImageBlurSvg)({widthInt:B,heightInt:X,blurWidth:c,blurHeight:u,blurDataURL:C||"",objectFit:$.objectFit})+'")':'url("'+S+'")',Y=i.includes($.objectFit)?"fill"===$.objectFit?"100% 100%":"cover":$.objectFit,K=J?{backgroundSize:Y,backgroundPosition:$.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:J}:{},Q=function(e){let{config:t,src:r,unoptimized:s,width:n,quality:i,sizes:a,loader:l}=e;if(s)return{src:r,srcSet:void 0,sizes:void 0};let{widths:o,kind:d}=function(e,t,r){let{deviceSizes:s,allSizes:n}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let s;s=e.exec(r);)t.push(parseInt(s[2]));if(t.length){let e=.01*Math.min(...t);return{widths:n.filter(t=>t>=s[0]*e),kind:"w"}}return{widths:n,kind:"w"}}return"number"!=typeof t?{widths:s,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>n.find(t=>t>=e)||n[n.length-1]))],kind:"x"}}(t,n,a),c=o.length-1;return{sizes:a||"w"!==d?a:"100vw",srcSet:o.map((e,s)=>l({config:t,src:r,quality:i,width:e})+" "+("w"===d?e:s+1)+d).join(", "),src:l({config:t,src:r,quality:i,width:o[c]})}}({config:d,src:m,unoptimized:p,width:B,quality:H,sizes:h,loader:F});return{props:{...I,loading:V?"lazy":x,fetchPriority:E,width:B,height:X,decoding:O,className:b,style:{...$,...K},sizes:Q.sizes,srcSet:Q.srcSet,src:N||Q.src},meta:{unoptimized:p,priority:f,placeholder:S,fill:y}}}},9588:(e,t,r)=>{Promise.resolve().then(r.bind(r,23440))},9693:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var s=r(37413),n=r(19854),i=r(12909),a=r(39916),l=r(43621),o=r(26373);let d=(0,o.A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),c=(0,o.A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]),u=(0,o.A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]),m=(0,o.A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);var h=r(40918),p=r(75234),f=r(70099),x=r.n(f);async function b(){let e=await (0,n.getServerSession)(i.N);e||(0,a.redirect)("/auth/signin");let t=await l.z.student.findUnique({where:{userId:e.user.id},include:{user:!0,currentGroupReference:{include:{teacherReference:!0}}}});return t||(0,a.redirect)("/auth/signin"),(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-base-content",children:"My Profile"}),(0,s.jsx)("p",{className:"text-base-content/70 mt-2",children:"View and manage your personal information"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,s.jsx)("div",{className:"lg:col-span-1",children:(0,s.jsx)("div",{className:"card bg-base-100 shadow-xl",children:(0,s.jsxs)("div",{className:"card-body text-center",children:[(0,s.jsx)("div",{className:"avatar placeholder mb-4",children:(0,s.jsx)("div",{className:"bg-neutral text-neutral-content rounded-full w-24 h-24",children:t.photoUrl?(0,s.jsx)(x(),{src:t.photoUrl,alt:t.user.name,width:96,height:96,className:"rounded-full"}):(0,s.jsx)("span",{className:"text-3xl",children:t.user.name.charAt(0)})})}),(0,s.jsx)("h2",{className:"card-title justify-center",children:t.user.name}),(0,s.jsx)("p",{className:"text-base-content/70",children:"Student"}),(0,s.jsxs)("div",{className:"badge badge-primary mt-2",children:["Level ",t.level]}),(0,s.jsx)("div",{className:"badge badge-outline mt-1",children:t.status})]})})}),(0,s.jsx)("div",{className:"lg:col-span-2",children:(0,s.jsx)("div",{className:"card bg-base-100 shadow-xl",children:(0,s.jsxs)("div",{className:"card-body",children:[(0,s.jsx)("h2",{className:"card-title mb-4",children:"Personal Information"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"form-control",children:[(0,s.jsx)("label",{className:"label",children:(0,s.jsx)("span",{className:"label-text",children:"Full Name"})}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(d,{className:"h-4 w-4 text-base-content/50"}),(0,s.jsx)("span",{children:t.user.name})]})]}),(0,s.jsxs)("div",{className:"form-control",children:[(0,s.jsx)("label",{className:"label",children:(0,s.jsx)("span",{className:"label-text",children:"Phone Number"})}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(c,{className:"h-4 w-4 text-base-content/50"}),(0,s.jsx)("span",{children:t.user.phone})]})]}),t.user.email&&(0,s.jsxs)("div",{className:"form-control",children:[(0,s.jsx)("label",{className:"label",children:(0,s.jsx)("span",{className:"label-text",children:"Email"})}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(u,{className:"h-4 w-4 text-base-content/50"}),(0,s.jsx)("span",{children:t.user.email})]})]}),(0,s.jsxs)("div",{className:"form-control",children:[(0,s.jsx)("label",{className:"label",children:(0,s.jsx)("span",{className:"label-text",children:"Branch"})}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(m,{className:"h-4 w-4 text-base-content/50"}),(0,s.jsx)("span",{children:t.branch})]})]}),t.dateOfBirth&&(0,s.jsxs)("div",{className:"form-control",children:[(0,s.jsx)("label",{className:"label",children:(0,s.jsx)("span",{className:"label-text",children:"Date of Birth"})}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 text-base-content/50"}),(0,s.jsx)("span",{children:new Date(t.dateOfBirth).toLocaleDateString()})]})]}),(0,s.jsxs)("div",{className:"form-control",children:[(0,s.jsx)("label",{className:"label",children:(0,s.jsx)("span",{className:"label-text",children:"Current Level"})}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 text-base-content/50"}),(0,s.jsx)("span",{children:t.level})]})]}),t.emergencyContact&&(0,s.jsxs)("div",{className:"form-control md:col-span-2",children:[(0,s.jsx)("label",{className:"label",children:(0,s.jsx)("span",{className:"label-text",children:"Emergency Contact"})}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(c,{className:"h-4 w-4 text-base-content/50"}),(0,s.jsx)("span",{children:t.emergencyContact})]})]}),t.address&&(0,s.jsxs)("div",{className:"form-control md:col-span-2",children:[(0,s.jsx)("label",{className:"label",children:(0,s.jsx)("span",{className:"label-text",children:"Address"})}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(m,{className:"h-4 w-4 text-base-content/50"}),(0,s.jsx)("span",{children:t.address})]})]})]}),(0,s.jsx)("div",{className:"card-actions justify-end mt-6",children:(0,s.jsx)("button",{className:"btn btn-primary",children:"Edit Profile"})})]})})})]}),t.currentGroupReference&&(0,s.jsx)("div",{className:"mt-6",children:(0,s.jsx)("div",{className:"card bg-base-100 shadow-xl",children:(0,s.jsxs)("div",{className:"card-body",children:[(0,s.jsx)("h2",{className:"card-title",children:"Current Group"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"label",children:(0,s.jsx)("span",{className:"label-text",children:"Group Name"})}),(0,s.jsx)("p",{className:"font-medium",children:t.currentGroupReference.name})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"label",children:(0,s.jsx)("span",{className:"label-text",children:"Course"})}),(0,s.jsx)("p",{className:"font-medium",children:t.currentGroupReference.courseName})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"label",children:(0,s.jsx)("span",{className:"label-text",children:"Teacher"})}),(0,s.jsx)("p",{className:"font-medium",children:t.currentGroupReference.teacherReference.name})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"label",children:(0,s.jsx)("span",{className:"label-text",children:"Room"})}),(0,s.jsx)("p",{className:"font-medium",children:t.currentGroupReference.room||"TBA"})]})]})]})})})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12756:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return s}});let r=["default","imgix","cloudinary","akamai","custom"],s={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>l});var s=r(13581),n=r(60890),i=r(85663),a=r(43621);let l={adapter:(0,n.y)(a.z),providers:[(0,s.A)({name:"credentials",credentials:{phone:{label:"Phone",type:"text"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.phone||!e?.password)return null;try{let t=await a.z.user.findUnique({where:{phone:e.phone},include:{studentProfile:!0}});if(!t||!await i.Ay.compare(e.password,t.password))return null;return{id:t.id,name:t.name,email:t.email||void 0,phone:t.phone,role:t.role,studentProfile:t.studentProfile||void 0}}catch(e){return console.error("Auth error:",e),null}}})],session:{strategy:"jwt",maxAge:2592e3},callbacks:{jwt:async({token:e,user:t})=>(t&&(e.role=t.role,e.phone=t.phone,e.studentProfile=t.studentProfile),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.phone=t.phone,e.user.studentProfile=t.studentProfile),e)},pages:{signIn:"/auth/signin",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET}},13027:(e,t,r)=>{"use strict";r.d(t,{StudentSidebar:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call StudentSidebar() from the server but StudentSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\components\\layout\\student-sidebar.tsx","StudentSidebar")},14959:(e,t,r)=>{"use strict";e.exports=r(94041).vendored.contexts.AmpContext},17903:(e,t,r)=>{"use strict";e.exports=r(94041).vendored.contexts.ImageConfigContext},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19349:(e,t,r)=>{Promise.resolve().then(r.bind(r,13027))},21122:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},21815:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,49603,23))},23440:(e,t,r)=>{"use strict";r.d(t,{SessionProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call SessionProvider() from the server but SessionProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\components\\providers\\SessionProvider.tsx","SessionProvider")},27894:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return s}});let r=["default","imgix","cloudinary","akamai","custom"],s={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30512:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return f},defaultHead:function(){return u}});let s=r(14985),n=r(40740),i=r(60687),a=n._(r(43210)),l=s._(r(47755)),o=r(14959),d=r(89513),c=r(34604);function u(e){void 0===e&&(e=!1);let t=[(0,i.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,i.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function m(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===a.default.Fragment?e.concat(a.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(50148);let h=["name","httpEquiv","charSet","itemProp"];function p(e,t){let{inAmpMode:r}=t;return e.reduce(m,[]).reverse().concat(u(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,s={};return n=>{let i=!0,a=!1;if(n.key&&"number"!=typeof n.key&&n.key.indexOf("$")>0){a=!0;let t=n.key.slice(n.key.indexOf("$")+1);e.has(t)?i=!1:e.add(t)}switch(n.type){case"title":case"base":t.has(n.type)?i=!1:t.add(n.type);break;case"meta":for(let e=0,t=h.length;e<t;e++){let t=h[e];if(n.props.hasOwnProperty(t))if("charSet"===t)r.has(t)?i=!1:r.add(t);else{let e=n.props[t],r=s[t]||new Set;("name"!==t||!a)&&r.has(e)?i=!1:(r.add(e),s[t]=r)}}}return i}}()).reverse().map((e,t)=>{let s=e.key||t;if(process.env.__NEXT_OPTIMIZE_FONTS&&!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,a.default.cloneElement(e,t)}return a.default.cloneElement(e,{key:s})})}let f=function(e){let{children:t}=e,r=(0,a.useContext)(o.AmpStateContext),s=(0,a.useContext)(d.HeadManagerContext);return(0,i.jsx)(l.default,{reduceComponentsToState:p,headManager:s,inAmpMode:(0,c.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},32091:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:s,width:n,quality:i}=e,a=i||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(s)+"&w="+n+"&q="+a+(s.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}}),r.__next_img_default=!0;let s=r},32917:(e,t,r)=>{Promise.resolve().then(r.bind(r,4631))},33873:e=>{"use strict";e.exports=require("path")},34604:(e,t)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:s=!1}=void 0===e?{}:e;return t||r&&s}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},41480:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:s,blurHeight:n,blurDataURL:i,objectFit:a}=e,l=s?40*s:t,o=n?40*n:r,d=l&&o?"viewBox='0 0 "+l+" "+o+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+d+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(d?"none":"contain"===a?"xMidYMid":"cover"===a?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+i+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},43015:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},43621:(e,t,r)=>{"use strict";r.d(t,{z:()=>n});let s=require("@prisma/client"),n=globalThis.prisma??new s.PrismaClient},44953:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return o}}),r(50148);let s=r(41480),n=r(12756),i=["-moz-initial","fill","none","scale-down",void 0];function a(e){return void 0!==e.default}function l(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function o(e,t){var r,o;let d,c,u,{src:m,sizes:h,unoptimized:p=!1,priority:f=!1,loading:x,className:b,quality:g,width:v,height:j,fill:y=!1,style:w,overrideSrc:N,onLoad:P,onLoadingComplete:_,placeholder:S="empty",blurDataURL:C,fetchPriority:E,decoding:O="async",layout:R,objectFit:M,objectPosition:A,lazyBoundary:k,lazyRoot:z,...I}=e,{imgConf:D,showAltText:q,blurComplete:T,defaultLoader:G}=t,U=D||n.imageConfigDefault;if("allSizes"in U)d=U;else{let e=[...U.deviceSizes,...U.imageSizes].sort((e,t)=>e-t),t=U.deviceSizes.sort((e,t)=>e-t),s=null==(r=U.qualities)?void 0:r.sort((e,t)=>e-t);d={...U,allSizes:e,deviceSizes:t,qualities:s}}if(void 0===G)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let F=I.loader||G;delete I.loader,delete I.srcSet;let L="__next_img_default"in F;if(L){if("custom"===d.loader)throw Object.defineProperty(Error('Image with src "'+m+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=F;F=t=>{let{config:r,...s}=t;return e(s)}}if(R){"fill"===R&&(y=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[R];e&&(w={...w,...e});let t={responsive:"100vw",fill:"100vw"}[R];t&&!h&&(h=t)}let W="",B=l(v),X=l(j);if((o=m)&&"object"==typeof o&&(a(o)||void 0!==o.src)){let e=a(m)?m.default:m;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(c=e.blurWidth,u=e.blurHeight,C=C||e.blurDataURL,W=e.src,!y)if(B||X){if(B&&!X){let t=B/e.width;X=Math.round(e.height*t)}else if(!B&&X){let t=X/e.height;B=Math.round(e.width*t)}}else B=e.width,X=e.height}let V=!f&&("lazy"===x||void 0===x);(!(m="string"==typeof m?m:W)||m.startsWith("data:")||m.startsWith("blob:"))&&(p=!0,V=!1),d.unoptimized&&(p=!0),L&&!d.dangerouslyAllowSVG&&m.split("?",1)[0].endsWith(".svg")&&(p=!0);let H=l(g),$=Object.assign(y?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:M,objectPosition:A}:{},q?{}:{color:"transparent"},w),J=T||"empty"===S?null:"blur"===S?'url("data:image/svg+xml;charset=utf-8,'+(0,s.getImageBlurSvg)({widthInt:B,heightInt:X,blurWidth:c,blurHeight:u,blurDataURL:C||"",objectFit:$.objectFit})+'")':'url("'+S+'")',Y=i.includes($.objectFit)?"fill"===$.objectFit?"100% 100%":"cover":$.objectFit,K=J?{backgroundSize:Y,backgroundPosition:$.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:J}:{},Q=function(e){let{config:t,src:r,unoptimized:s,width:n,quality:i,sizes:a,loader:l}=e;if(s)return{src:r,srcSet:void 0,sizes:void 0};let{widths:o,kind:d}=function(e,t,r){let{deviceSizes:s,allSizes:n}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let s;s=e.exec(r);)t.push(parseInt(s[2]));if(t.length){let e=.01*Math.min(...t);return{widths:n.filter(t=>t>=s[0]*e),kind:"w"}}return{widths:n,kind:"w"}}return"number"!=typeof t?{widths:s,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>n.find(t=>t>=e)||n[n.length-1]))],kind:"x"}}(t,n,a),c=o.length-1;return{sizes:a||"w"!==d?a:"100vw",srcSet:o.map((e,s)=>l({config:t,src:r,quality:i,width:e})+" "+("w"===d?e:s+1)+d).join(", "),src:l({config:t,src:r,quality:i,width:o[c]})}}({config:d,src:m,unoptimized:p,width:B,quality:H,sizes:h,loader:F});return{props:{...I,loading:V?"lazy":x,fetchPriority:E,width:B,height:X,decoding:O,className:b,style:{...$,...K},sizes:Q.sizes,srcSet:Q.srcSet,src:N||Q.src},meta:{unoptimized:p,priority:f,placeholder:S,fill:y}}}},46533:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return j}});let s=r(14985),n=r(40740),i=r(60687),a=n._(r(43210)),l=s._(r(51215)),o=s._(r(30512)),d=r(44953),c=r(12756),u=r(17903);r(50148);let m=r(69148),h=s._(r(1933)),p=r(53038),f={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function x(e,t,r,s,n,i,a){let l=null==e?void 0:e.src;e&&e["data-loaded-src"]!==l&&(e["data-loaded-src"]=l,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&n(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let s=!1,n=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>s,isPropagationStopped:()=>n,persist:()=>{},preventDefault:()=>{s=!0,t.preventDefault()},stopPropagation:()=>{n=!0,t.stopPropagation()}})}(null==s?void 0:s.current)&&s.current(e)}}))}function b(e){return a.use?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let g=(0,a.forwardRef)((e,t)=>{let{src:r,srcSet:s,sizes:n,height:l,width:o,decoding:d,className:c,style:u,fetchPriority:m,placeholder:h,loading:f,unoptimized:g,fill:v,onLoadRef:j,onLoadingCompleteRef:y,setBlurComplete:w,setShowAltText:N,sizesInput:P,onLoad:_,onError:S,...C}=e,E=(0,a.useCallback)(e=>{e&&(S&&(e.src=e.src),e.complete&&x(e,h,j,y,w,g,P))},[r,h,j,y,w,S,g,P]),O=(0,p.useMergedRef)(t,E);return(0,i.jsx)("img",{...C,...b(m),loading:f,width:o,height:l,decoding:d,"data-nimg":v?"fill":"1",className:c,style:u,sizes:n,srcSet:s,src:r,ref:O,onLoad:e=>{x(e.currentTarget,h,j,y,w,g,P)},onError:e=>{N(!0),"empty"!==h&&w(!0),S&&S(e)}})});function v(e){let{isAppRouter:t,imgAttributes:r}=e,s={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,...b(r.fetchPriority)};return t&&l.default.preload?(l.default.preload(r.src,s),null):(0,i.jsx)(o.default,{children:(0,i.jsx)("link",{rel:"preload",href:r.srcSet?void 0:r.src,...s},"__nimg-"+r.src+r.srcSet+r.sizes)})}let j=(0,a.forwardRef)((e,t)=>{let r=(0,a.useContext)(m.RouterContext),s=(0,a.useContext)(u.ImageConfigContext),n=(0,a.useMemo)(()=>{var e;let t=f||s||c.imageConfigDefault,r=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),n=t.deviceSizes.sort((e,t)=>e-t),i=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:r,deviceSizes:n,qualities:i}},[s]),{onLoad:l,onLoadingComplete:o}=e,p=(0,a.useRef)(l);(0,a.useEffect)(()=>{p.current=l},[l]);let x=(0,a.useRef)(o);(0,a.useEffect)(()=>{x.current=o},[o]);let[b,j]=(0,a.useState)(!1),[y,w]=(0,a.useState)(!1),{props:N,meta:P}=(0,d.getImgProps)(e,{defaultLoader:h.default,imgConf:n,blurComplete:b,showAltText:y});return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(g,{...N,unoptimized:P.unoptimized,placeholder:P.placeholder,fill:P.fill,onLoadRef:p,onLoadingCompleteRef:x,setBlurComplete:j,setShowAltText:w,sizesInput:e.sizes,ref:t}),P.priority?(0,i.jsx)(v,{isAppRouter:!r,imgAttributes:N}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},47755:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let s=r(43210),n=()=>{},i=()=>{};function a(e){var t;let{headManager:r,reduceComponentsToState:a}=e;function l(){if(r&&r.mountedInstances){let t=s.Children.toArray(Array.from(r.mountedInstances).filter(Boolean));r.updateHead(a(t,e))}}return null==r||null==(t=r.mountedInstances)||t.add(e.children),l(),n(()=>{var t;return null==r||null==(t=r.mountedInstances)||t.add(e.children),()=>{var t;null==r||null==(t=r.mountedInstances)||t.delete(e.children)}}),n(()=>(r&&(r._pendingUpdate=l),()=>{r&&(r._pendingUpdate=l)})),i(()=>(r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null),()=>{r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null)})),null}},49603:(e,t,r)=>{let{createProxy:s}=r(39844);e.exports=s("C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\node_modules\\next\\dist\\client\\image-component.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58607:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,46533,23))},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63144:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(37413),n=r(13027);function i({children:e}){return(0,s.jsxs)("div",{className:"drawer lg:drawer-open",children:[(0,s.jsx)("input",{id:"drawer-toggle",type:"checkbox",className:"drawer-toggle"}),(0,s.jsxs)("div",{className:"drawer-content flex flex-col",children:[(0,s.jsxs)("div",{className:"navbar bg-base-100 shadow-sm lg:hidden",children:[(0,s.jsx)("div",{className:"flex-none",children:(0,s.jsx)("label",{htmlFor:"drawer-toggle",className:"btn btn-square btn-ghost",children:(0,s.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})}),(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)("h1",{className:"text-xl font-bold",children:"Student Portal"})})]}),(0,s.jsx)("main",{className:"flex-1 bg-base-100",children:e})]}),(0,s.jsx)(n.StudentSidebar,{})]})}},69148:(e,t,r)=>{"use strict";e.exports=r(94041).vendored.contexts.RouterContext},69268:(e,t,r)=>{Promise.resolve().then(r.bind(r,76242))},70099:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return o},getImageProps:function(){return l}});let s=r(72639),n=r(9131),i=r(49603),a=s._(r(32091));function l(e){let{props:t}=(0,n.getImgProps)(e,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let o=i.Image},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},75234:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(26373).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},76242:(e,t,r)=>{"use strict";r.d(t,{SessionProvider:()=>i});var s=r(60687),n=r(82136);function i({children:e}){return(0,s.jsx)(n.SessionProvider,{children:e})}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83183:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},89513:(e,t,r)=>{"use strict";e.exports=r(94041).vendored.contexts.HeadManagerContext},90898:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(65239),n=r(48088),i=r(88170),a=r.n(i),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let d={children:["",{children:["dashboard",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,9693)),"C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\profile\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,63144)),"C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\profile\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/dashboard/profile/page",pathname:"/dashboard/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>d});var s=r(37413),n=r(22376),i=r.n(n),a=r(68726),l=r.n(a);r(61135);var o=r(23440);let d={title:"Student Portal - Innovative Centre",description:"Student portal for Innovative Centre"};function c({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${i().variable} ${l().variable} antialiased`,children:(0,s.jsx)(o.SessionProvider,{children:e})})})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[243,310,658,663,819,418,228],()=>r(90898));module.exports=s})();