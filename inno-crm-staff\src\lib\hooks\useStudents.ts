'use client'

import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'

export interface Student {
  id: string
  name: string
  phone: string
  level?: string
  branch: string
  emergencyContact?: string
  status: string
  createdAt: string
  updatedAt: string
  currentGroup?: {
    name: string
  }
}

export interface CreateStudentData {
  name: string
  phone: string
  email?: string
  level: string
  branch: string
  emergencyContact?: string
}

export interface UpdateStudentData {
  name?: string
  phone?: string
  level?: string
  branch?: string
  emergencyContact?: string
  status?: string
  currentGroupId?: string
}

export interface StudentsResponse {
  students: Student[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

export function useStudents() {
  const { data: session } = useSession()
  const [students, setStudents] = useState<Student[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  })

  const fetchStudents = useCallback(async (params?: {
    page?: number
    limit?: number
    search?: string
    status?: string
    branch?: string
  }) => {
    if (!session) return

    setLoading(true)
    setError(null)

    try {
      const searchParams = new URLSearchParams()
      if (params?.page) searchParams.set('page', params.page.toString())
      if (params?.limit) searchParams.set('limit', params.limit.toString())
      if (params?.search) searchParams.set('search', params.search)
      if (params?.status) searchParams.set('status', params.status)
      if (params?.branch) searchParams.set('branch', params.branch)

      const response = await fetch(`/api/students?${searchParams}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch students')
      }

      const data: StudentsResponse = await response.json()
      setStudents(data.students)
      setPagination(data.pagination)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }, [session])

  const createStudent = async (data: CreateStudentData): Promise<Student> => {
    if (!session) throw new Error('Not authenticated')

    const response = await fetch('/api/students', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to create student')
    }

    const student = await response.json()
    setStudents(prev => [student, ...prev])
    return student
  }

  const updateStudent = async (id: string, data: UpdateStudentData): Promise<Student> => {
    if (!session) throw new Error('Not authenticated')

    const response = await fetch(`/api/students/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to update student')
    }

    const updatedStudent = await response.json()
    setStudents(prev => 
      prev.map(student => 
        student.id === id ? updatedStudent : student
      )
    )
    return updatedStudent
  }

  const deleteStudent = async (id: string): Promise<void> => {
    if (!session) throw new Error('Not authenticated')

    const response = await fetch(`/api/students/${id}`, {
      method: 'DELETE',
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to delete student')
    }

    setStudents(prev => prev.filter(student => student.id !== id))
  }

  const getStudent = async (id: string): Promise<Student> => {
    if (!session) throw new Error('Not authenticated')

    const response = await fetch(`/api/students/${id}`)
    
    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to fetch student')
    }

    return response.json()
  }

  useEffect(() => {
    if (session) {
      fetchStudents()
    }
  }, [session, fetchStudents])

  return {
    students,
    loading,
    error,
    pagination,
    fetchStudents,
    createStudent,
    updateStudent,
    deleteStudent,
    getStudent,
  }
}
