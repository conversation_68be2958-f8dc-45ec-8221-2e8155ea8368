import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/database'
import { studentsApiClient } from '@/lib/api-clients/students'
import { z } from 'zod'

// Middleware to verify inter-server requests
function verifyInterServerRequest(request: NextRequest) {
  const apiKey = request.headers.get('X-API-Key')
  const serverSource = request.headers.get('X-Server-Source')
  
  if (!apiKey || apiKey !== process.env.STUDENTS_API_KEY) {
    return false
  }
  
  if (serverSource !== 'students') {
    return false
  }
  
  return true
}

const updateStudentSchema = z.object({
  name: z.string().min(1).optional(),
  phone: z.string().min(1).optional(),
  level: z.enum(['A1', 'A2', 'B1', 'B2', 'IELTS', 'SAT', 'MATH', 'KIDS']).optional(),
  branch: z.string().min(1).optional(),
  emergencyContact: z.string().optional(),
  status: z.enum(['ACTIVE', 'DROPPED', 'PAUSED', 'COMPLETED']).optional(),
  currentGroupId: z.string().optional(),
})

// GET /api/inter-server/students/[id] - Get student data
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  if (!verifyInterServerRequest(request)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  try {
    const student = await prisma.studentReference.findUnique({
      where: { id: params.id },
      include: {
        currentGroup: {
          include: {
            course: {
              select: {
                name: true,
                level: true,
                price: true,
              }
            },
            teacher: {
              include: {
                user: {
                  select: {
                    name: true,
                    phone: true,
                    email: true,
                  }
                }
              }
            }
          }
        },
        paymentOverview: {
          orderBy: { createdAt: 'desc' },
          take: 10,
        }
      }
    })

    if (!student) {
      return NextResponse.json({ error: 'Student not found' }, { status: 404 })
    }

    return NextResponse.json(student)
  } catch (error) {
    console.error('Error fetching student:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PUT /api/inter-server/students/[id] - Update student data
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  if (!verifyInterServerRequest(request)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  try {
    const body = await request.json()
    const validatedData = updateStudentSchema.parse(body)

    // Check if student exists
    const existingStudent = await prisma.studentReference.findUnique({
      where: { id: params.id }
    })

    if (!existingStudent) {
      return NextResponse.json({ error: 'Student not found' }, { status: 404 })
    }

    // Update student in staff database
    const updatedStudent = await prisma.studentReference.update({
      where: { id: params.id },
      data: {
        ...validatedData,
        updatedAt: new Date(),
        lastSyncedAt: new Date(),
      },
      include: {
        currentGroup: {
          select: {
            name: true,
          }
        }
      }
    })

    // Sync with students database if needed
    try {
      await studentsApiClient.syncStudent(params.id)
    } catch (error) {
      console.warn('Failed to sync with students database:', error)
      // Don't fail the request if sync fails
    }

    return NextResponse.json(updatedStudent)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }
    
    console.error('Error updating student:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST /api/inter-server/students/[id]/sync - Sync student data
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  if (!verifyInterServerRequest(request)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  try {
    // Get latest student data from staff database
    const student = await prisma.studentReference.findUnique({
      where: { id: params.id },
      include: {
        currentGroup: {
          include: {
            course: true,
            teacher: {
              include: {
                user: true,
              }
            }
          }
        }
      }
    })

    if (!student) {
      return NextResponse.json({ error: 'Student not found' }, { status: 404 })
    }

    // Update last synced timestamp
    await prisma.studentReference.update({
      where: { id: params.id },
      data: { lastSyncedAt: new Date() }
    })

    return NextResponse.json({
      student,
      syncTimestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Error syncing student:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
