'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { X } from 'lucide-react'
import { Student, CreateStudentData, UpdateStudentData } from '@/lib/hooks/useStudents'

const studentSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  phone: z.string().min(1, 'Phone is required'),
  email: z.string().email().optional().or(z.literal('')),
  level: z.enum(['A1', 'A2', 'B1', 'B2', 'IELTS', 'SAT', 'MATH', 'KIDS']),
  branch: z.string().min(1, 'Branch is required'),
  emergencyContact: z.string().optional(),
  status: z.enum(['ACTIVE', 'DROPPED', 'PAUSED', 'COMPLETED']).optional(),
})

type StudentFormData = z.infer<typeof studentSchema>

interface StudentModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: CreateStudentData | UpdateStudentData) => Promise<void>
  student?: Student | null
  loading?: boolean
}

export function StudentModal({
  isOpen,
  onClose,
  onSubmit,
  student
}: StudentModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const isEditing = !!student

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<StudentFormData>({
    resolver: zodResolver(studentSchema),
    defaultValues: {
      name: '',
      phone: '',
      email: '',
      level: 'A1',
      branch: 'main',
      emergencyContact: '',
      status: 'ACTIVE',
    }
  })

  useEffect(() => {
    if (student) {
      reset({
        name: student.name,
        phone: student.phone,
        email: '',
        level: student.level || 'A1',
        branch: student.branch,
        emergencyContact: student.emergencyContact || '',
        status: student.status || 'ACTIVE',
      })
    } else {
      reset({
        name: '',
        phone: '',
        email: '',
        level: 'A1',
        branch: 'main',
        emergencyContact: '',
        status: 'ACTIVE',
      })
    }
  }, [student, reset])

  const handleFormSubmit = async (data: StudentFormData) => {
    setIsSubmitting(true)
    try {
      const submitData = {
        ...data,
        email: data.email || undefined,
        emergencyContact: data.emergencyContact || undefined,
      }
      
      if (isEditing) {
        const { status, ...updateData } = submitData
        await onSubmit({ ...updateData, status })
      } else {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { status, ...createData } = submitData
        await onSubmit(createData)
      }
      
      onClose()
    } catch (error) {
      console.error('Error submitting student:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="modal modal-open">
      <div className="modal-box w-11/12 max-w-2xl">
        <div className="flex justify-between items-center mb-6">
          <h3 className="font-bold text-lg">
            {isEditing ? 'Edit Student' : 'Add New Student'}
          </h3>
          <button
            onClick={onClose}
            className="btn btn-ghost btn-sm btn-circle"
            disabled={isSubmitting}
          >
            <X className="h-4 w-4" />
          </button>
        </div>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="form-control">
              <label className="label">
                <span className="label-text">Full Name *</span>
              </label>
              <input
                type="text"
                className={`input input-bordered ${errors.name ? 'input-error' : ''}`}
                {...register('name')}
              />
              {errors.name && (
                <label className="label">
                  <span className="label-text-alt text-error">
                    {errors.name.message}
                  </span>
                </label>
              )}
            </div>

            <div className="form-control">
              <label className="label">
                <span className="label-text">Phone Number *</span>
              </label>
              <input
                type="text"
                className={`input input-bordered ${errors.phone ? 'input-error' : ''}`}
                {...register('phone')}
              />
              {errors.phone && (
                <label className="label">
                  <span className="label-text-alt text-error">
                    {errors.phone.message}
                  </span>
                </label>
              )}
            </div>

            <div className="form-control">
              <label className="label">
                <span className="label-text">Email</span>
              </label>
              <input
                type="email"
                className={`input input-bordered ${errors.email ? 'input-error' : ''}`}
                {...register('email')}
              />
              {errors.email && (
                <label className="label">
                  <span className="label-text-alt text-error">
                    {errors.email.message}
                  </span>
                </label>
              )}
            </div>

            <div className="form-control">
              <label className="label">
                <span className="label-text">Level *</span>
              </label>
              <select
                className={`select select-bordered ${errors.level ? 'select-error' : ''}`}
                {...register('level')}
              >
                <option value="A1">A1</option>
                <option value="A2">A2</option>
                <option value="B1">B1</option>
                <option value="B2">B2</option>
                <option value="IELTS">IELTS</option>
                <option value="SAT">SAT</option>
                <option value="MATH">Math</option>
                <option value="KIDS">Kids</option>
              </select>
              {errors.level && (
                <label className="label">
                  <span className="label-text-alt text-error">
                    {errors.level.message}
                  </span>
                </label>
              )}
            </div>

            <div className="form-control">
              <label className="label">
                <span className="label-text">Branch *</span>
              </label>
              <select
                className={`select select-bordered ${errors.branch ? 'select-error' : ''}`}
                {...register('branch')}
              >
                <option value="main">Main Branch</option>
              </select>
              {errors.branch && (
                <label className="label">
                  <span className="label-text-alt text-error">
                    {errors.branch.message}
                  </span>
                </label>
              )}
            </div>

            {isEditing && (
              <div className="form-control">
                <label className="label">
                  <span className="label-text">Status</span>
                </label>
                <select
                  className={`select select-bordered ${errors.status ? 'select-error' : ''}`}
                  {...register('status')}
                >
                  <option value="ACTIVE">Active</option>
                  <option value="PAUSED">Paused</option>
                  <option value="DROPPED">Dropped</option>
                  <option value="COMPLETED">Completed</option>
                </select>
                {errors.status && (
                  <label className="label">
                    <span className="label-text-alt text-error">
                      {errors.status.message}
                    </span>
                  </label>
                )}
              </div>
            )}

            <div className="form-control md:col-span-2">
              <label className="label">
                <span className="label-text">Emergency Contact</span>
              </label>
              <input
                type="text"
                className="input input-bordered"
                {...register('emergencyContact')}
              />
            </div>
          </div>

          <div className="modal-action">
            <button
              type="button"
              onClick={onClose}
              className="btn btn-ghost"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className={`btn btn-primary ${isSubmitting ? 'loading' : ''}`}
              disabled={isSubmitting}
            >
              {isSubmitting 
                ? 'Saving...' 
                : isEditing 
                  ? 'Update Student' 
                  : 'Add Student'
              }
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
