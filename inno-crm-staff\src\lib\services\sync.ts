import { prisma } from '@/lib/database'
import { studentsApiClient } from '@/lib/api-clients/students'
import { Role } from '@prisma/client'

export class SyncService {
  // Sync student data from staff to students database
  static async syncStudentToStudentsDB(studentId: string) {
    try {
      const student = await prisma.studentReference.findUnique({
        where: { id: studentId },
        include: {
          currentGroup: {
            include: {
              course: true,
              teacher: {
                include: {
                  user: true,
                }
              }
            }
          }
        }
      })

      if (!student) {
        throw new Error('Student not found')
      }

      // Update student in students database
      await studentsApiClient.updateStudent(studentId, {
        name: student.name,
        phone: student.phone,
        level: student.level || 'A1',
        branch: student.branch,
        emergencyContact: student.emergencyContact,
        status: student.status,
      })

      // Update sync timestamp
      await prisma.studentReference.update({
        where: { id: studentId },
        data: { lastSyncedAt: new Date() }
      })

      return { success: true, message: 'Student synced successfully' }
    } catch (error) {
      console.error('Error syncing student to students DB:', error)
      throw error
    }
  }

  // Sync all students that need updating
  static async syncAllStudents() {
    try {
      // Get students that haven't been synced in the last hour
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
      
      const studentsToSync = await prisma.studentReference.findMany({
        where: {
          OR: [
            { lastSyncedAt: null },
            { lastSyncedAt: { lt: oneHourAgo } },
            { updatedAt: { gt: prisma.studentReference.fields.lastSyncedAt } }
          ]
        },
        take: 50, // Limit to prevent overwhelming the system
      })

      const results = []
      for (const student of studentsToSync) {
        try {
          await this.syncStudentToStudentsDB(student.id)
          results.push({ id: student.id, status: 'success' })
        } catch (error) {
          results.push({ 
            id: student.id, 
            status: 'error', 
            error: error instanceof Error ? error.message : 'Unknown error' 
          })
        }
      }

      return {
        totalProcessed: studentsToSync.length,
        successful: results.filter(r => r.status === 'success').length,
        failed: results.filter(r => r.status === 'error').length,
        results
      }
    } catch (error) {
      console.error('Error in bulk sync:', error)
      throw error
    }
  }

  // Sync group assignments
  static async syncGroupAssignment(groupId: string, studentIds: string[]) {
    try {
      // Update students in staff database
      await prisma.studentReference.updateMany({
        where: { id: { in: studentIds } },
        data: { 
          currentGroupId: groupId,
          updatedAt: new Date(),
        }
      })

      // Notify students service about group assignment
      await studentsApiClient.notifyGroupChange(groupId, studentIds)

      // Log activity
      await prisma.activityLog.createMany({
        data: studentIds.map(studentId => ({
          userId: 'system',
          userRole: 'ADMIN' as Role,
          action: 'UPDATE',
          resource: 'student',
          resourceId: studentId,
          details: {
            action: 'group_assignment',
            groupId,
            timestamp: new Date().toISOString()
          }
        }))
      })

      return { success: true, message: 'Group assignment synced successfully' }
    } catch (error) {
      console.error('Error syncing group assignment:', error)
      throw error
    }
  }

  // Get sync status for dashboard
  static async getSyncStatus() {
    try {
      const totalStudents = await prisma.studentReference.count()
      
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
      const recentlySynced = await prisma.studentReference.count({
        where: {
          lastSyncedAt: { gte: oneHourAgo }
        }
      })

      const neverSynced = await prisma.studentReference.count({
        where: { lastSyncedAt: null }
      })

      const needsSync = await prisma.studentReference.count({
        where: {
          OR: [
            { lastSyncedAt: null },
            { lastSyncedAt: { lt: oneHourAgo } },
          ]
        }
      })

      return {
        totalStudents,
        recentlySynced,
        neverSynced,
        needsSync,
        syncPercentage: totalStudents > 0 ? Math.round((recentlySynced / totalStudents) * 100) : 0,
        lastSyncCheck: new Date().toISOString()
      }
    } catch (error) {
      console.error('Error getting sync status:', error)
      throw error
    }
  }

  // Manual sync trigger for specific student
  static async triggerStudentSync(studentId: string, userId: string) {
    try {
      await this.syncStudentToStudentsDB(studentId)

      // Log manual sync activity
      await prisma.activityLog.create({
        data: {
          userId,
          userRole: 'ADMIN' as Role,
          action: 'SYNC',
          resource: 'student',
          resourceId: studentId,
          details: {
            action: 'manual_sync',
            timestamp: new Date().toISOString()
          }
        }
      })

      return { success: true, message: 'Student synced manually' }
    } catch (error) {
      console.error('Error in manual sync:', error)
      throw error
    }
  }
}

// Background sync job (can be called by cron or scheduled task)
export async function runBackgroundSync() {
  try {
    console.log('Starting background sync...')
    const result = await SyncService.syncAllStudents()
    console.log('Background sync completed:', result)
    return result
  } catch (error) {
    console.error('Background sync failed:', error)
    throw error
  }
}
