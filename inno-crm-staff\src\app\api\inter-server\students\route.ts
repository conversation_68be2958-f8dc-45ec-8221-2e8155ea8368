import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/database'
import { studentsApiClient } from '@/lib/api-clients/students'
import { z } from 'zod'

// Middleware to verify inter-server requests
function verifyInterServerRequest(request: NextRequest) {
  const apiKey = request.headers.get('X-API-Key')
  const serverSource = request.headers.get('X-Server-Source')
  
  if (!apiKey || apiKey !== process.env.STUDENTS_API_KEY) {
    return false
  }
  
  if (serverSource !== 'students') {
    return false
  }
  
  return true
}

const createStudentSchema = z.object({
  name: z.string().min(1),
  phone: z.string().min(1),
  email: z.string().email().optional(),
  password: z.string().min(6),
  level: z.enum(['A1', 'A2', 'B1', 'B2', 'IELTS', 'SAT', 'MATH', 'KIDS']),
  branch: z.string().min(1),
  emergencyContact: z.string().optional(),
})

// POST /api/inter-server/students - Create student in both databases
export async function POST(request: NextRequest) {
  if (!verifyInterServerRequest(request)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  try {
    const body = await request.json()
    const validatedData = createStudentSchema.parse(body)

    // Check if student already exists in staff database
    const existingStudent = await prisma.studentReference.findUnique({
      where: { phone: validatedData.phone }
    })

    if (existingStudent) {
      return NextResponse.json(
        { error: 'Student already exists' },
        { status: 400 }
      )
    }

    // Generate unique student ID
    const studentId = `student_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Create student reference in staff database
    const studentReference = await prisma.studentReference.create({
      data: {
        id: studentId,
        name: validatedData.name,
        phone: validatedData.phone,
        level: validatedData.level,
        branch: validatedData.branch,
        emergencyContact: validatedData.emergencyContact,
        status: 'ACTIVE',
      }
    })

    // Create student in students database via API
    try {
      const studentData = await studentsApiClient.createStudent({
        ...validatedData,
        id: studentId,
      })

      return NextResponse.json({
        id: studentId,
        staffRecord: studentReference,
        studentRecord: studentData,
      }, { status: 201 })
    } catch (error) {
      // If students database creation fails, rollback staff database
      await prisma.studentReference.delete({
        where: { id: studentId }
      })
      
      throw error
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }
    
    console.error('Error creating student:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// GET /api/inter-server/students - Get students for sync
export async function GET(request: NextRequest) {
  if (!verifyInterServerRequest(request)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  try {
    const { searchParams } = new URL(request.url)
    const lastSync = searchParams.get('lastSync')
    const limit = parseInt(searchParams.get('limit') || '100')

    const where: { updatedAt?: { gte: Date } } = {}
    if (lastSync) {
      where.updatedAt = {
        gte: new Date(lastSync)
      }
    }

    const students = await prisma.studentReference.findMany({
      where,
      orderBy: { updatedAt: 'desc' },
      take: limit,
      include: {
        currentGroup: {
          select: {
            id: true,
            name: true,
            course: {
              select: {
                name: true,
                level: true,
              }
            },
            teacher: {
              select: {
                id: true,
                user: {
                  select: {
                    name: true,
                  }
                }
              }
            }
          }
        }
      }
    })

    return NextResponse.json({
      students,
      syncTimestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Error fetching students for sync:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
