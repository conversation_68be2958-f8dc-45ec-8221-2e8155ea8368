(()=>{var e={};e.id=889,e.ids=[889],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4631:(e,s,t)=>{"use strict";t.d(s,{StudentSidebar:()=>j});var r=t(60687),n=t(85814),a=t.n(n),i=t(16189),d=t(82136),o=t(49384),l=t(82348),c=t(53411),h=t(40228),m=t(86561),x=t(82080),u=t(85778),p=t(58887),b=t(58869),v=t(40083);let f=[{name:"Dashboard",href:"/dashboard",icon:c.A},{name:"My Classes",href:"/dashboard/classes",icon:h.A},{name:"Assessments",href:"/dashboard/assessments",icon:m.A},{name:"Progress",href:"/dashboard/progress",icon:x.A},{name:"Payments",href:"/dashboard/payments",icon:u.A},{name:"Messages",href:"/dashboard/messages",icon:p.A},{name:"Profile",href:"/dashboard/profile",icon:b.A}];function j(){let e=(0,i.usePathname)(),{data:s}=(0,d.useSession)();return(0,r.jsxs)("div",{className:"drawer-side",children:[(0,r.jsx)("label",{htmlFor:"drawer-toggle",className:"drawer-overlay"}),(0,r.jsxs)("aside",{className:"min-h-full w-64 bg-base-200 flex flex-col",children:[(0,r.jsx)("div",{className:"p-4",children:(0,r.jsx)("h1",{className:"text-xl font-bold text-primary",children:"Student Portal"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("ul",{className:"menu p-4 space-y-2",children:f.map(s=>{let t=s.icon,n=e===s.href;return(0,r.jsx)("li",{children:(0,r.jsxs)(a(),{href:s.href,className:function(...e){return(0,l.QP)((0,o.$)(e))}("flex items-center space-x-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors",n?"bg-primary text-primary-content":"text-base-content hover:bg-base-300"),children:[(0,r.jsx)(t,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:s.name})]})},s.name)})})}),s&&(0,r.jsxs)("div",{className:"p-4 border-t border-base-300",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,r.jsx)("div",{className:"avatar placeholder",children:(0,r.jsx)("div",{className:"bg-neutral text-neutral-content rounded-full w-10",children:(0,r.jsx)(b.A,{className:"h-5 w-5"})})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-base-content truncate",children:s.user.name}),(0,r.jsx)("p",{className:"text-xs text-base-content/70 truncate",children:"Student"})]})]}),(0,r.jsxs)("button",{onClick:()=>{(0,d.signOut)({callbackUrl:"/auth/signin"})},className:"btn btn-ghost btn-sm w-full justify-start",children:[(0,r.jsx)(v.A,{className:"h-4 w-4"}),"Sign Out"]})]})]})]})}},5148:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(26373).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},9588:(e,s,t)=>{Promise.resolve().then(t.bind(t,23440))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,s,t)=>{"use strict";t.d(s,{N:()=>d});var r=t(13581),n=t(60890),a=t(85663),i=t(43621);let d={adapter:(0,n.y)(i.z),providers:[(0,r.A)({name:"credentials",credentials:{phone:{label:"Phone",type:"text"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.phone||!e?.password)return null;try{let s=await i.z.user.findUnique({where:{phone:e.phone},include:{studentProfile:!0}});if(!s||!await a.Ay.compare(e.password,s.password))return null;return{id:s.id,name:s.name,email:s.email||void 0,phone:s.phone,role:s.role,studentProfile:s.studentProfile||void 0}}catch(e){return console.error("Auth error:",e),null}}})],session:{strategy:"jwt",maxAge:2592e3},callbacks:{jwt:async({token:e,user:s})=>(s&&(e.role=s.role,e.phone=s.phone,e.studentProfile=s.studentProfile),e),session:async({session:e,token:s})=>(s&&(e.user.id=s.sub,e.user.role=s.role,e.user.phone=s.phone,e.user.studentProfile=s.studentProfile),e)},pages:{signIn:"/auth/signin",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET}},13027:(e,s,t)=>{"use strict";t.d(s,{StudentSidebar:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call StudentSidebar() from the server but StudentSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\components\\layout\\student-sidebar.tsx","StudentSidebar")},18155:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var r=t(37413),n=t(19854),a=t(12909),i=t(39916),d=t(43621),o=t(26373);let l=(0,o.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),c=(0,o.A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);var h=t(5148),m=t(40918);async function x(){let e=await (0,n.getServerSession)(a.N);e||(0,i.redirect)("/auth/signin");let s=await d.z.student.findUnique({where:{userId:e.user.id},include:{payments:{orderBy:{createdAt:"desc"}}}});s||(0,i.redirect)("/auth/signin");let t=s.payments.filter(e=>"PAID"===e.status).reduce((e,s)=>e+s.amount.toNumber(),0),o=s.payments.filter(e=>"DEBT"===e.status).reduce((e,s)=>e+s.amount.toNumber(),0),x=e=>{switch(e){case"PAID":return"badge-success";case"DEBT":return"badge-error";case"REFUNDED":return"badge-warning";default:return"badge-ghost"}},u=e=>{switch(e){case"PAID":return(0,r.jsx)(l,{className:"h-4 w-4"});case"DEBT":return(0,r.jsx)(c,{className:"h-4 w-4"});default:return(0,r.jsx)(h.A,{className:"h-4 w-4"})}};return(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-base-content",children:"Payment History"}),(0,r.jsx)("p",{className:"text-base-content/70 mt-2",children:"View your payment records and account balance"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"card bg-base-100 shadow-xl",children:(0,r.jsx)("div",{className:"card-body",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"card-title text-sm font-medium text-base-content/70",children:"Total Paid"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-success",children:["$",t]})]}),(0,r.jsx)(l,{className:"h-8 w-8 text-success"})]})})}),(0,r.jsx)("div",{className:"card bg-base-100 shadow-xl",children:(0,r.jsx)("div",{className:"card-body",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"card-title text-sm font-medium text-base-content/70",children:"Outstanding Debt"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-error",children:["$",o]})]}),(0,r.jsx)(c,{className:"h-8 w-8 text-error"})]})})}),(0,r.jsx)("div",{className:"card bg-base-100 shadow-xl",children:(0,r.jsx)("div",{className:"card-body",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"card-title text-sm font-medium text-base-content/70",children:"Total Payments"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-base-content",children:s.payments.length})]}),(0,r.jsx)(h.A,{className:"h-8 w-8 text-primary"})]})})})]}),(0,r.jsx)("div",{className:"card bg-base-100 shadow-xl",children:(0,r.jsxs)("div",{className:"card-body",children:[(0,r.jsx)("h2",{className:"card-title mb-4",children:"Payment History"}),s.payments.length>0?(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"table table-zebra w-full",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{children:"Date"}),(0,r.jsx)("th",{children:"Description"}),(0,r.jsx)("th",{children:"Amount"}),(0,r.jsx)("th",{children:"Method"}),(0,r.jsx)("th",{children:"Status"}),(0,r.jsx)("th",{children:"Due Date"})]})}),(0,r.jsx)("tbody",{children:s.payments.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 text-base-content/50"}),(0,r.jsx)("span",{children:e.paidDate?new Date(e.paidDate).toLocaleDateString():new Date(e.createdAt).toLocaleDateString()})]})}),(0,r.jsx)("td",{children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:e.description||"Course Payment"}),e.transactionId&&(0,r.jsxs)("div",{className:"text-sm text-base-content/70",children:["ID: ",e.transactionId]})]})}),(0,r.jsx)("td",{children:(0,r.jsxs)("span",{className:"font-bold",children:["$",e.amount.toNumber()]})}),(0,r.jsx)("td",{children:(0,r.jsx)("span",{className:"badge badge-outline",children:e.method})}),(0,r.jsx)("td",{children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[u(e.status),(0,r.jsx)("span",{className:`badge ${x(e.status)}`,children:e.status})]})}),(0,r.jsx)("td",{children:e.dueDate?(0,r.jsx)("span",{className:new Date(e.dueDate)<new Date&&"DEBT"===e.status?"text-error":"text-base-content",children:new Date(e.dueDate).toLocaleDateString()}):(0,r.jsx)("span",{className:"text-base-content/50",children:"N/A"})})]},e.id))})]})}):(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(h.A,{className:"h-12 w-12 text-base-content/30 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-base-content/70",children:"No payment history found"})]})]})}),o>0&&(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsxs)("div",{className:"alert alert-error",children:[(0,r.jsx)(c,{className:"h-5 w-5"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-bold",children:"Outstanding Payment"}),(0,r.jsxs)("div",{className:"text-sm",children:["You have $",o," in outstanding payments. Please contact the administration to resolve this."]})]})]})})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19349:(e,s,t)=>{Promise.resolve().then(t.bind(t,13027))},23440:(e,s,t)=>{"use strict";t.d(s,{SessionProvider:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call SessionProvider() from the server but SessionProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\components\\providers\\SessionProvider.tsx","SessionProvider")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32917:(e,s,t)=>{Promise.resolve().then(t.bind(t,4631))},33873:e=>{"use strict";e.exports=require("path")},43015:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},43621:(e,s,t)=>{"use strict";t.d(s,{z:()=>n});let r=require("@prisma/client"),n=globalThis.prisma??new r.PrismaClient},46180:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>h,pages:()=>c,routeModule:()=>m,tree:()=>l});var r=t(65239),n=t(48088),a=t(88170),i=t.n(a),d=t(30893),o={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);t.d(s,o);let l={children:["",{children:["dashboard",{children:["payments",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,18155)),"C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\payments\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,63144)),"C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\payments\\page.tsx"],h={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/dashboard/payments/page",pathname:"/dashboard/payments",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63144:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(37413),n=t(13027);function a({children:e}){return(0,r.jsxs)("div",{className:"drawer lg:drawer-open",children:[(0,r.jsx)("input",{id:"drawer-toggle",type:"checkbox",className:"drawer-toggle"}),(0,r.jsxs)("div",{className:"drawer-content flex flex-col",children:[(0,r.jsxs)("div",{className:"navbar bg-base-100 shadow-sm lg:hidden",children:[(0,r.jsx)("div",{className:"flex-none",children:(0,r.jsx)("label",{htmlFor:"drawer-toggle",className:"btn btn-square btn-ghost",children:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("h1",{className:"text-xl font-bold",children:"Student Portal"})})]}),(0,r.jsx)("main",{className:"flex-1 bg-base-100",children:e})]}),(0,r.jsx)(n.StudentSidebar,{})]})}},69268:(e,s,t)=>{Promise.resolve().then(t.bind(t,76242))},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},76242:(e,s,t)=>{"use strict";t.d(s,{SessionProvider:()=>a});var r=t(60687),n=t(82136);function a({children:e}){return(0,r.jsx)(n.SessionProvider,{children:e})}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83183:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},94431:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c,metadata:()=>l});var r=t(37413),n=t(22376),a=t.n(n),i=t(68726),d=t.n(i);t(61135);var o=t(23440);let l={title:"Student Portal - Innovative Centre",description:"Student portal for Innovative Centre"};function c({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${a().variable} ${d().variable} antialiased`,children:(0,r.jsx)(o.SessionProvider,{children:e})})})}},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[243,310,658,663,819,418,228],()=>t(46180));module.exports=r})();