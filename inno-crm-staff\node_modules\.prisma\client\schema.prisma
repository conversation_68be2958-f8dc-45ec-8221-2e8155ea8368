generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Staff-specific models
model User {
  id        String   @id @default(cuid())
  email     String?  @unique
  phone     String   @unique
  name      String
  role      Role     @default(ADMIN)
  password  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  teacherProfile Teacher?
  activityLogs   ActivityLog[]
  callRecords    CallRecord[]
  messages       Message[]
  announcements  Announcement[]

  @@map("users")
}

model Teacher {
  id         String      @id @default(cuid())
  userId     String      @unique
  subject    String
  experience Int?
  salary     Decimal?
  branch     String
  photoUrl   String?
  tier       TeacherTier @default(NEW)
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt

  // Relations
  user          User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  groups        Group[]
  assignedLeads Lead[]

  @@map("teachers")
}

model Group {
  id        String   @id @default(cuid())
  name      String   @unique
  courseId  String
  teacherId String
  capacity  Int      @default(20)
  schedule  Json // JSON string for schedule
  room      String?
  cabinetId String?
  branch    String
  startDate DateTime
  endDate   DateTime
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  course            Course             @relation(fields: [courseId], references: [id])
  teacher           Teacher            @relation(fields: [teacherId], references: [id])
  cabinet           Cabinet?           @relation(fields: [cabinetId], references: [id])
  assignedLeads     Lead[]
  studentReferences StudentReference[]

  @@map("groups")
}

model Course {
  id          String   @id @default(cuid())
  name        String   @unique
  level       Level
  description String?
  duration    Int // in weeks
  price       Decimal
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  groups Group[]

  @@map("courses")
}

model Lead {
  id                String     @id @default(cuid())
  name              String
  phone             String     @unique
  coursePreference  String
  status            LeadStatus @default(NEW)
  source            String?
  notes             String?
  branch            String     @default("main")
  assignedTo        String?
  followUpDate      DateTime?
  callStartedAt     DateTime?
  callEndedAt       DateTime?
  callDuration      Int? // in seconds
  assignedGroupId   String?
  assignedTeacherId String?
  assignedAt        DateTime?
  archivedAt        DateTime?
  createdAt         DateTime   @default(now())
  updatedAt         DateTime   @updatedAt

  // Relations
  assignedGroup   Group?       @relation(fields: [assignedGroupId], references: [id])
  assignedTeacher Teacher?     @relation(fields: [assignedTeacherId], references: [id])
  callRecords     CallRecord[]

  @@map("leads")
}

model Cabinet {
  id        String   @id @default(cuid())
  name      String   @unique
  number    String   @unique
  capacity  Int      @default(20)
  floor     Int?
  building  String?
  branch    String
  equipment Json? // JSON string for equipment list
  isActive  Boolean  @default(true)
  notes     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  groups Group[]

  @@map("cabinets")
}

// Reference table for students (minimal data from students server)
model StudentReference {
  id               String        @id // Same ID as in students database
  name             String
  phone            String        @unique
  currentGroupId   String?
  status           StudentStatus
  branch           String
  level            Level?
  emergencyContact String?
  createdAt        DateTime      @default(now())
  updatedAt        DateTime      @updatedAt
  lastSyncedAt     DateTime      @default(now())

  // Relations
  currentGroup    Group?            @relation(fields: [currentGroupId], references: [id])
  paymentOverview PaymentOverview[]

  @@map("student_references")
}

// Financial overview (admin view of all payments)
model PaymentOverview {
  id                 String        @id @default(cuid())
  studentReferenceId String
  amount             Decimal
  method             PaymentMethod
  status             PaymentStatus @default(PAID)
  description        String?
  transactionId      String?
  dueDate            DateTime?
  paidDate           DateTime?
  createdAt          DateTime      @default(now())
  updatedAt          DateTime      @updatedAt

  // Relations
  studentReference StudentReference @relation(fields: [studentReferenceId], references: [id])

  @@map("payment_overview")
}

model ActivityLog {
  id         String   @id @default(cuid())
  userId     String
  userRole   Role
  action     String // e.g., "CREATE", "UPDATE", "DELETE", "VIEW"
  resource   String // e.g., "student", "payment", "group"
  resourceId String? // ID of the affected resource
  details    Json? // Additional details about the action
  ipAddress  String?
  userAgent  String?
  createdAt  DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id])

  @@map("activity_logs")
}

model CallRecord {
  id           String    @id @default(cuid())
  leadId       String
  userId       String // User who made the call
  startedAt    DateTime
  endedAt      DateTime?
  duration     Int? // in seconds
  notes        String?
  recordingUrl String? // URL to call recording if available
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  // Relations
  lead Lead @relation(fields: [leadId], references: [id], onDelete: Cascade)
  user User @relation(fields: [userId], references: [id])

  @@map("call_records")
}

model Message {
  id            String    @id @default(cuid())
  subject       String
  content       String
  recipientType String // 'ALL', 'STUDENTS', 'TEACHERS', 'ACADEMIC_MANAGERS', 'SPECIFIC'
  recipientIds  String[] // Array of user IDs for specific recipients
  priority      String    @default("MEDIUM") // 'LOW', 'MEDIUM', 'HIGH'
  status        String    @default("DRAFT") // 'DRAFT', 'SENT', 'FAILED'
  sentAt        DateTime?
  senderId      String
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  sender User @relation(fields: [senderId], references: [id])

  @@map("messages")
}

model Announcement {
  id             String   @id @default(cuid())
  title          String
  content        String
  priority       String   @default("MEDIUM") // 'LOW', 'MEDIUM', 'HIGH'
  targetAudience String   @default("ALL") // 'ALL', 'STUDENTS', 'TEACHERS', 'ACADEMIC_MANAGERS'
  isActive       Boolean  @default(true)
  authorId       String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  author User @relation(fields: [authorId], references: [id])

  @@map("announcements")
}

// Enums
enum Role {
  ADMIN
  MANAGER
  TEACHER
  RECEPTION
  CASHIER
  ACADEMIC_MANAGER
}

enum Level {
  A1
  A2
  B1
  B2
  IELTS
  SAT
  MATH
  KIDS
}

enum LeadStatus {
  NEW
  CALLING
  CALL_COMPLETED
  GROUP_ASSIGNED
  ARCHIVED
  NOT_INTERESTED
}

enum StudentStatus {
  ACTIVE
  DROPPED
  PAUSED
  COMPLETED
}

enum PaymentMethod {
  CASH
  CARD
}

enum PaymentStatus {
  PAID
  DEBT
  REFUNDED
}

enum TeacherTier {
  A_LEVEL
  B_LEVEL
  C_LEVEL
  NEW
}
