import "next-auth"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      name: string
      phone: string
      email?: string
      role: string
      studentProfile?: Record<string, unknown>
    }
  }

  interface User {
    id: string
    name: string
    phone: string
    email?: string
    role: string
    studentProfile?: Record<string, unknown>
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    role: string
    phone: string
    studentProfile?: Record<string, unknown>
  }
}
