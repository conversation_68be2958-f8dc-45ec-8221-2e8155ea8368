import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/database'

// Middleware to verify inter-server requests
function verifyInterServerRequest(request: NextRequest) {
  const apiKey = request.headers.get('X-API-Key')
  const serverSource = request.headers.get('X-Server-Source')
  
  if (!apiKey || apiKey !== process.env.STUDENTS_API_KEY) {
    return false
  }
  
  if (serverSource !== 'students') {
    return false
  }
  
  return true
}

// GET /api/inter-server/groups/[id] - Get group data for students service
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  if (!verifyInterServerRequest(request)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  try {
    const group = await prisma.group.findUnique({
      where: { id: params.id },
      include: {
        course: {
          select: {
            name: true,
            level: true,
            description: true,
            duration: true,
            price: true,
          }
        },
        teacher: {
          include: {
            user: {
              select: {
                name: true,
                phone: true,
                email: true,
              }
            }
          }
        },
        cabinet: {
          select: {
            name: true,
            number: true,
            capacity: true,
            floor: true,
            building: true,
          }
        }
      }
    })

    if (!group) {
      return NextResponse.json({ error: 'Group not found' }, { status: 404 })
    }

    // Transform data for students service
    const groupData = {
      id: group.id,
      name: group.name,
      courseName: group.course.name,
      courseLevel: group.course.level,
      courseDescription: group.course.description,
      courseDuration: group.course.duration,
      coursePrice: group.course.price,
      teacherId: group.teacher.id,
      teacherName: group.teacher.user.name,
      teacherPhone: group.teacher.user.phone,
      teacherEmail: group.teacher.user.email,
      teacherSubject: group.teacher.subject,
      teacherPhotoUrl: group.teacher.photoUrl,
      capacity: group.capacity,
      schedule: group.schedule,
      room: group.room,
      cabinetInfo: group.cabinet,
      branch: group.branch,
      startDate: group.startDate,
      endDate: group.endDate,
      isActive: group.isActive,
      createdAt: group.createdAt,
      updatedAt: group.updatedAt,
    }

    return NextResponse.json(groupData)
  } catch (error) {
    console.error('Error fetching group:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST /api/inter-server/groups/[id]/sync - Sync group data
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  if (!verifyInterServerRequest(request)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  try {
    const group = await prisma.group.findUnique({
      where: { id: params.id },
      include: {
        course: true,
        teacher: {
          include: {
            user: true,
          }
        },
        cabinet: true,
        assignedLeads: {
          select: {
            id: true,
            name: true,
            phone: true,
            status: true,
          }
        },
        studentReferences: {
          select: {
            id: true,
            name: true,
            phone: true,
            status: true,
            level: true,
          }
        }
      }
    })

    if (!group) {
      return NextResponse.json({ error: 'Group not found' }, { status: 404 })
    }

    return NextResponse.json({
      group,
      syncTimestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Error syncing group:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
