import { prisma } from '@/lib/database'
import { staffApiClient } from '@/lib/api-clients/staff'

export class SyncService {
  // Sync group data from staff database
  static async syncGroupFromStaff(groupId: string) {
    try {
      const groupData = await staffApiClient.getGroup(groupId)

      // Check if group reference exists
      const existingGroup = await prisma.groupReference.findUnique({
        where: { id: groupId }
      })

      // Check if teacher reference exists
      let teacherReference = await prisma.teacherReference.findUnique({
        where: { id: groupData.teacherId }
      })

      if (!teacherReference) {
        // Create teacher reference
        teacherReference = await prisma.teacherReference.create({
          data: {
            id: groupData.teacherId,
            name: groupData.teacherName,
            subject: groupData.teacherSubject,
            branch: groupData.branch,
            photoUrl: groupData.teacherPhotoUrl,
            lastSyncedAt: new Date(),
            syncVersion: 1,
          }
        })
      }

      if (existingGroup) {
        // Update existing group reference
        const updatedGroup = await prisma.groupReference.update({
          where: { id: groupId },
          data: {
            name: groupData.name,
            courseName: groupData.courseName,
            schedule: groupData.schedule,
            room: groupData.room,
            branch: groupData.branch,
            startDate: new Date(groupData.startDate),
            endDate: new Date(groupData.endDate),
            isActive: groupData.isActive,
            lastSyncedAt: new Date(),
            syncVersion: existingGroup.syncVersion + 1,
          }
        })
        return updatedGroup
      } else {
        // Create new group reference
        const newGroup = await prisma.groupReference.create({
          data: {
            id: groupId,
            name: groupData.name,
            teacherReferenceId: groupData.teacherId,
            courseName: groupData.courseName,
            schedule: groupData.schedule,
            room: groupData.room,
            branch: groupData.branch,
            startDate: new Date(groupData.startDate),
            endDate: new Date(groupData.endDate),
            isActive: groupData.isActive,
            lastSyncedAt: new Date(),
            syncVersion: 1,
          }
        })
        return newGroup
      }
    } catch (error) {
      console.error('Error syncing group from staff:', error)
      throw error
    }
  }

  // Sync teacher data from staff database
  static async syncTeacherFromStaff(teacherId: string) {
    try {
      const teacherData = await staffApiClient.getTeacher(teacherId)

      const existingTeacher = await prisma.teacherReference.findUnique({
        where: { id: teacherId }
      })

      if (existingTeacher) {
        // Update existing teacher reference
        const updatedTeacher = await prisma.teacherReference.update({
          where: { id: teacherId },
          data: {
            name: teacherData.name,
            subject: teacherData.subject,
            branch: teacherData.branch,
            photoUrl: teacherData.photoUrl,
            lastSyncedAt: new Date(),
            syncVersion: existingTeacher.syncVersion + 1,
          }
        })
        return updatedTeacher
      } else {
        // Create new teacher reference
        const newTeacher = await prisma.teacherReference.create({
          data: {
            id: teacherId,
            name: teacherData.name,
            subject: teacherData.subject,
            branch: teacherData.branch,
            photoUrl: teacherData.photoUrl,
            lastSyncedAt: new Date(),
            syncVersion: 1,
          }
        })
        return newTeacher
      }
    } catch (error) {
      console.error('Error syncing teacher from staff:', error)
      throw error
    }
  }

  // Report attendance to staff database
  static async reportAttendance(data: {
    studentId: string
    classReferenceId: string
    groupReferenceId: string
    status: 'PRESENT' | 'ABSENT' | 'LATE' | 'EXCUSED'
    notes?: string
  }) {
    try {
      // Create attendance record locally
      const attendance = await prisma.attendance.create({
        data: {
          studentId: data.studentId,
          classReferenceId: data.classReferenceId,
          groupReferenceId: data.groupReferenceId,
          status: data.status,
          notes: data.notes,
        }
      })

      // Report to staff database
      await staffApiClient.reportAttendance(data)

      return attendance
    } catch (error) {
      console.error('Error reporting attendance:', error)
      throw error
    }
  }

  // Report assessment results to staff database
  static async reportAssessment(data: {
    studentId: string
    groupReferenceId: string
    testName: string
    type: 'LEVEL_TEST' | 'PROGRESS_TEST' | 'FINAL_EXAM' | 'GROUP_TEST'
    score?: number
    maxScore?: number
    passed: boolean
    results?: Record<string, unknown>
  }) {
    try {
      // Create assessment record locally
      const assessment = await prisma.assessment.create({
        data: {
          studentId: data.studentId,
          groupReferenceId: data.groupReferenceId,
          testName: data.testName,
          type: data.type,
          score: data.score,
          maxScore: data.maxScore,
          passed: data.passed,
          results: data.results,
          completedAt: new Date(),
        }
      })

      // Report to staff database
      await staffApiClient.reportAssessment({
        ...data,
        groupId: data.groupReferenceId,
      })

      return assessment
    } catch (error) {
      console.error('Error reporting assessment:', error)
      throw error
    }
  }

  // Sync payment data from staff database
  static async syncPaymentData(studentId: string) {
    try {
      const paymentData = await staffApiClient.getPaymentOverview(studentId)

      // Update local payment records
      for (const payment of paymentData) {
        await prisma.payment.upsert({
          where: { 
            id: payment.id 
          },
          update: {
            amount: payment.amount,
            method: payment.method,
            status: payment.status,
            description: payment.description,
            dueDate: payment.dueDate ? new Date(payment.dueDate) : null,
            paidDate: payment.paidDate ? new Date(payment.paidDate) : null,
            updatedAt: new Date(),
          },
          create: {
            id: payment.id,
            studentId: payment.studentId,
            amount: payment.amount,
            method: payment.method,
            status: payment.status,
            description: payment.description,
            dueDate: payment.dueDate ? new Date(payment.dueDate) : null,
            paidDate: payment.paidDate ? new Date(payment.paidDate) : null,
          }
        })
      }

      return paymentData
    } catch (error) {
      console.error('Error syncing payment data:', error)
      throw error
    }
  }

  // Get sync status for dashboard
  static async getSyncStatus() {
    try {
      const totalGroups = await prisma.groupReference.count()
      const totalTeachers = await prisma.teacherReference.count()
      
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
      
      const recentlyUpdatedGroups = await prisma.groupReference.count({
        where: { lastSyncedAt: { gte: oneHourAgo } }
      })

      const recentlyUpdatedTeachers = await prisma.teacherReference.count({
        where: { lastSyncedAt: { gte: oneHourAgo } }
      })

      return {
        groups: {
          total: totalGroups,
          recentlyUpdated: recentlyUpdatedGroups,
          syncPercentage: totalGroups > 0 ? Math.round((recentlyUpdatedGroups / totalGroups) * 100) : 0
        },
        teachers: {
          total: totalTeachers,
          recentlyUpdated: recentlyUpdatedTeachers,
          syncPercentage: totalTeachers > 0 ? Math.round((recentlyUpdatedTeachers / totalTeachers) * 100) : 0
        },
        lastSyncCheck: new Date().toISOString()
      }
    } catch (error) {
      console.error('Error getting sync status:', error)
      throw error
    }
  }

  // Notify staff about student status change
  static async notifyStudentStatusChange(studentId: string, status: string, reason?: string) {
    try {
      await staffApiClient.notifyStudentStatusChange(studentId, status, reason)
      
      return { success: true, message: 'Status change notification sent' }
    } catch (error) {
      console.error('Error notifying status change:', error)
      throw error
    }
  }
}

// Background sync job for students service
export async function runBackgroundSync() {
  try {
    console.log('Starting students service background sync...')
    
    // Sync group and teacher references that are outdated
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
    
    const outdatedGroups = await prisma.groupReference.findMany({
      where: {
        OR: [
          { lastSyncedAt: null },
          { lastSyncedAt: { lt: oneHourAgo } }
        ]
      },
      take: 20
    })

    const outdatedTeachers = await prisma.teacherReference.findMany({
      where: {
        OR: [
          { lastSyncedAt: null },
          { lastSyncedAt: { lt: oneHourAgo } }
        ]
      },
      take: 20
    })

    const results = {
      groupsProcessed: 0,
      teachersProcessed: 0,
      errors: []
    }

    // Sync groups
    for (const group of outdatedGroups) {
      try {
        await SyncService.syncGroupFromStaff(group.id)
        results.groupsProcessed++
      } catch (error) {
        results.errors.push(`Group ${group.id}: ${error}`)
      }
    }

    // Sync teachers
    for (const teacher of outdatedTeachers) {
      try {
        await SyncService.syncTeacherFromStaff(teacher.id)
        results.teachersProcessed++
      } catch (error) {
        results.errors.push(`Teacher ${teacher.id}: ${error}`)
      }
    }

    console.log('Students service background sync completed:', results)
    return results
  } catch (error) {
    console.error('Students service background sync failed:', error)
    throw error
  }
}
