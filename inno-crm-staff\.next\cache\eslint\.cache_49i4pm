[{"C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\app\\dashboard\\layout.tsx": "1", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\app\\dashboard\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\layout\\sidebar.tsx": "3", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\ui\\dashboard-card.tsx": "4", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\lib\\auth.ts": "5", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\lib\\database.ts": "6", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\lib\\utils.ts": "7", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\layout.tsx": "8", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\page.tsx": "9", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\layout.tsx": "10", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\components\\layout\\sidebar.tsx": "12", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\components\\ui\\dashboard-card.tsx": "13", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\lib\\auth.ts": "14", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\lib\\database.ts": "15", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\lib\\utils.ts": "16", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\types\\next-auth.d.ts": "17", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\auth\\[...nextauth]\\route.ts": "18", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\inter-server\\groups\\[id]\\route.ts": "19", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\inter-server\\students\\route.ts": "20", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\inter-server\\students\\[id]\\route.ts": "21", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\students\\route.ts": "22", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\students\\[id]\\route.ts": "23", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\auth\\error\\page.tsx": "24", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\auth\\signin\\page.tsx": "25", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\leads\\page.tsx": "26", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\students\\page.tsx": "27", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\teachers\\page.tsx": "28", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\components\\modals\\StudentModal.tsx": "29", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\components\\providers\\SessionProvider.tsx": "30", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\lib\\api-clients\\students.ts": "31", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\lib\\hooks\\useStudents.ts": "32", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\lib\\services\\sync.ts": "33"}, {"size": 1120, "mtime": 1750433989876, "results": "34", "hashOfConfig": "35"}, {"size": 2426, "mtime": 1750433980033, "results": "36", "hashOfConfig": "35"}, {"size": 1948, "mtime": 1750433966391, "results": "37", "hashOfConfig": "35"}, {"size": 1037, "mtime": 1750433955183, "results": "38", "hashOfConfig": "35"}, {"size": 1718, "mtime": 1750437925068, "results": "39", "hashOfConfig": "35"}, {"size": 271, "mtime": 1750433059805, "results": "40", "hashOfConfig": "35"}, {"size": 166, "mtime": 1750433075388, "results": "41", "hashOfConfig": "35"}, {"size": 1120, "mtime": 1750435130914, "results": "42", "hashOfConfig": "35"}, {"size": 4192, "mtime": 1750439177197, "results": "43", "hashOfConfig": "35"}, {"size": 852, "mtime": 1750438529472, "results": "44", "hashOfConfig": "35"}, {"size": 314, "mtime": 1750438615840, "results": "45", "hashOfConfig": "35"}, {"size": 3251, "mtime": 1750438600895, "results": "46", "hashOfConfig": "35"}, {"size": 1037, "mtime": 1750435067552, "results": "47", "hashOfConfig": "35"}, {"size": 2116, "mtime": 1750440884896, "results": "48", "hashOfConfig": "35"}, {"size": 296, "mtime": 1750441244373, "results": "49", "hashOfConfig": "35"}, {"size": 166, "mtime": 1750435106432, "results": "50", "hashOfConfig": "35"}, {"size": 952, "mtime": 1750438454398, "results": "51", "hashOfConfig": "35"}, {"size": 157, "mtime": 1750438464235, "results": "52", "hashOfConfig": "35"}, {"size": 3961, "mtime": 1750440281312, "results": "53", "hashOfConfig": "35"}, {"size": 4418, "mtime": 1750440747279, "results": "54", "hashOfConfig": "35"}, {"size": 5127, "mtime": 1750440261414, "results": "55", "hashOfConfig": "35"}, {"size": 4377, "mtime": 1750440768183, "results": "56", "hashOfConfig": "35"}, {"size": 5551, "mtime": 1750440787214, "results": "57", "hashOfConfig": "35"}, {"size": 1712, "mtime": 1750438495520, "results": "58", "hashOfConfig": "35"}, {"size": 4188, "mtime": 1750442636528, "results": "59", "hashOfConfig": "35"}, {"size": 8035, "mtime": 1750440814996, "results": "60", "hashOfConfig": "35"}, {"size": 9685, "mtime": 1750442663759, "results": "61", "hashOfConfig": "35"}, {"size": 7265, "mtime": 1750440934018, "results": "62", "hashOfConfig": "35"}, {"size": 9146, "mtime": 1750442684548, "results": "63", "hashOfConfig": "35"}, {"size": 321, "mtime": 1750438539749, "results": "64", "hashOfConfig": "35"}, {"size": 3354, "mtime": 1750439719180, "results": "65", "hashOfConfig": "35"}, {"size": 4540, "mtime": 1750441382006, "results": "66", "hashOfConfig": "35"}, {"size": 5949, "mtime": 1750440361268, "results": "67", "hashOfConfig": "35"}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "8ci4t3", {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\app\\dashboard\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\layout\\sidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\ui\\dashboard-card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\lib\\auth.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\lib\\database.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\components\\layout\\sidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\components\\ui\\dashboard-card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\lib\\auth.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\lib\\database.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\types\\next-auth.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\auth\\[...nextauth]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\inter-server\\groups\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\inter-server\\students\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\inter-server\\students\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\students\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\api\\students\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\auth\\error\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\auth\\signin\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\leads\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\students\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\teachers\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\components\\modals\\StudentModal.tsx", ["167"], ["168"], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\components\\providers\\SessionProvider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\lib\\api-clients\\students.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\lib\\hooks\\useStudents.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\lib\\services\\sync.ts", [], [], {"ruleId": null, "message": "169", "line": 91, "column": 9, "severity": 1, "nodeType": null, "fix": "170"}, {"ruleId": "171", "severity": 2, "message": "172", "line": 96, "column": 17, "nodeType": null, "messageId": "173", "endLine": 96, "endColumn": 23, "suppressions": "174"}, "Unused eslint-disable directive (no problems were reported from '@typescript-eslint/no-unused-vars').", {"range": "175", "text": "176"}, "@typescript-eslint/no-unused-vars", "'status' is assigned a value but never used.", "unusedVar", ["177"], [2326, 2387], " ", {"kind": "178", "justification": "179"}, "directive", ""]