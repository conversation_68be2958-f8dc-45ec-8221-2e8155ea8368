import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/database'
import { Role, StudentStatus } from '@prisma/client'
import { z } from 'zod'

const createStudentSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  phone: z.string().min(1, 'Phone is required'),
  email: z.string().email().optional(),
  level: z.enum(['A1', 'A2', 'B1', 'B2', 'IELTS', 'SAT', 'MATH', 'KIDS']),
  branch: z.string().min(1, 'Branch is required'),
  emergencyContact: z.string().optional(),
})

// GET /api/students - List students with pagination and filters
export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions)
  
  if (!session || !['ADMIN', 'MANAGER', 'ACADEMIC_MANAGER'].includes(session.user.role)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  const { searchParams } = new URL(request.url)
  const page = parseInt(searchParams.get('page') || '1')
  const limit = parseInt(searchParams.get('limit') || '20')
  const search = searchParams.get('search') || ''
  const status = searchParams.get('status') || ''
  const branch = searchParams.get('branch') || ''

  try {
    const where: {
      OR?: Array<{ name: { contains: string; mode: 'insensitive' } } | { phone: { contains: string; mode: 'insensitive' } }>;
      status?: StudentStatus;
      branch?: string;
    } = {}
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { phone: { contains: search, mode: 'insensitive' } },
      ]
    }
    
    if (status) {
      where.status = status as StudentStatus
    }
    
    if (branch) {
      where.branch = branch
    }

    const [students, total] = await Promise.all([
      prisma.studentReference.findMany({
        where,
        include: {
          currentGroup: {
            select: {
              name: true,
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.studentReference.count({ where })
    ])

    return NextResponse.json({
      students,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching students:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST /api/students - Create new student
export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions)
  
  if (!session || !['ADMIN', 'MANAGER'].includes(session.user.role)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  try {
    const body = await request.json()
    const validatedData = createStudentSchema.parse(body)

    // Check if phone number already exists
    const existingStudent = await prisma.studentReference.findUnique({
      where: { phone: validatedData.phone }
    })

    if (existingStudent) {
      return NextResponse.json(
        { error: 'Student with this phone number already exists' },
        { status: 400 }
      )
    }

    // Create student reference in staff database
    const studentReference = await prisma.studentReference.create({
      data: {
        id: `student_${Date.now()}`, // Generate unique ID
        name: validatedData.name,
        phone: validatedData.phone,
        level: validatedData.level,
        branch: validatedData.branch,
        emergencyContact: validatedData.emergencyContact,
        status: 'ACTIVE',
      }
    })

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: session.user.id,
        userRole: session.user.role as Role,
        action: 'CREATE',
        resource: 'student',
        resourceId: studentReference.id,
        details: {
          studentName: validatedData.name,
          phone: validatedData.phone,
        }
      }
    })

    return NextResponse.json(studentReference, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }
    
    console.error('Error creating student:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
