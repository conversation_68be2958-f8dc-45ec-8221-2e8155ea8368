(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[680],{3738:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>m});var a=r(5155),l=r(2115),n=r(2108),t=r(5695),c=r(2177),i=r(8778),o=r(1153);let d=o.z.object({phone:o.z.string().min(1,"Phone number is required"),password:o.z.string().min(1,"Password is required")});function m(){let[e,s]=(0,l.useState)(!1),[r,o]=(0,l.useState)(""),m=(0,t.useRouter)(),{register:h,handleSubmit:p,formState:{errors:u}}=(0,c.mN)({resolver:(0,i.u)(d)}),x=async e=>{s(!0),o("");try{let s=await (0,n.signIn)("credentials",{phone:e.phone,password:e.password,redirect:!1});if(null==s?void 0:s.error)o("Invalid phone number or password");else{let e=await (0,n.getSession)();(null==e?void 0:e.user)&&m.push("/dashboard")}}catch(e){console.error("Sign in error:",e),o("An error occurred. Please try again.")}finally{s(!1)}};return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-base-200",children:(0,a.jsx)("div",{className:"card w-full max-w-md bg-base-100 shadow-xl",children:(0,a.jsxs)("div",{className:"card-body",children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-primary",children:"Student Portal"}),(0,a.jsx)("p",{className:"text-base-content/70 mt-2",children:"Sign in to your account"})]}),(0,a.jsxs)("form",{onSubmit:p(x),className:"space-y-4",children:[(0,a.jsxs)("div",{className:"form-control",children:[(0,a.jsx)("label",{className:"label",children:(0,a.jsx)("span",{className:"label-text",children:"Phone Number"})}),(0,a.jsx)("input",{type:"text",placeholder:"Enter your phone number",className:"input input-bordered w-full ".concat(u.phone?"input-error":""),...h("phone")}),u.phone&&(0,a.jsx)("label",{className:"label",children:(0,a.jsx)("span",{className:"label-text-alt text-error",children:u.phone.message})})]}),(0,a.jsxs)("div",{className:"form-control",children:[(0,a.jsx)("label",{className:"label",children:(0,a.jsx)("span",{className:"label-text",children:"Password"})}),(0,a.jsx)("input",{type:"password",placeholder:"Enter your password",className:"input input-bordered w-full ".concat(u.password?"input-error":""),...h("password")}),u.password&&(0,a.jsx)("label",{className:"label",children:(0,a.jsx)("span",{className:"label-text-alt text-error",children:u.password.message})})]}),r&&(0,a.jsx)("div",{className:"alert alert-error",children:(0,a.jsx)("span",{children:r})}),(0,a.jsx)("div",{className:"form-control mt-6",children:(0,a.jsx)("button",{type:"submit",className:"btn btn-primary w-full ".concat(e?"loading":""),disabled:e,children:e?"Signing in...":"Sign In"})})]}),(0,a.jsx)("div",{className:"divider",children:"OR"}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("p",{className:"text-sm text-base-content/70",children:"Need help? Contact your teacher or administrator"})})]})})})}},7570:(e,s,r)=>{Promise.resolve().then(r.bind(r,3738))}},e=>{var s=s=>e(e.s=s);e.O(0,[108,424,441,684,358],()=>s(7570)),_N_E=e.O()}]);