(()=>{var e={};e.id=119,e.ids=[119],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},35144:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>h,serverHooks:()=>A,workAsyncStorage:()=>P,workUnitAsyncStorage:()=>y});var n={};r.r(n),r.d(n,{GET:()=>p,POST:()=>m,PUT:()=>f});var s=r(96559),a=r(48088),o=r(37719),u=r(32190),i=r(43621),d=r(45697);function c(e){let t=e.headers.get("X-API-Key"),r=e.headers.get("X-Server-Source");return!!t&&t===process.env.STAFF_API_KEY&&"staff"===r}let l=d.z.object({name:d.z.string().min(1).optional(),phone:d.z.string().min(1).optional(),email:d.z.string().email().optional(),level:d.z.enum(["A1","A2","B1","B2","IELTS","SAT","MATH","KIDS"]).optional(),branch:d.z.string().min(1).optional(),emergencyContact:d.z.string().optional(),status:d.z.enum(["ACTIVE","DROPPED","PAUSED","COMPLETED"]).optional(),currentGroupReferenceId:d.z.string().optional()});async function p(e,{params:t}){if(!c(e))return u.NextResponse.json({error:"Unauthorized"},{status:401});try{let{id:e}=await t,r=await i.z.user.findUnique({where:{id:e},include:{studentProfile:{include:{currentGroupReference:{include:{teacherReference:!0}},payments:{orderBy:{createdAt:"desc"}},attendances:{orderBy:{createdAt:"desc"},take:20},assessments:{orderBy:{createdAt:"desc"}}}}}});if(!r||"STUDENT"!==r.role)return u.NextResponse.json({error:"Student not found"},{status:404});let n={id:r.id,name:r.name,phone:r.phone,email:r.email,level:r.studentProfile?.level,branch:r.studentProfile?.branch,emergencyContact:r.studentProfile?.emergencyContact,status:r.studentProfile?.status,currentGroup:r.studentProfile?.currentGroupReference,payments:r.studentProfile?.payments,attendance:r.studentProfile?.attendances,assessments:r.studentProfile?.assessments,createdAt:r.createdAt,updatedAt:r.updatedAt};return u.NextResponse.json(n)}catch(e){return console.error("Error fetching student:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}async function f(e,{params:t}){if(!c(e))return u.NextResponse.json({error:"Unauthorized"},{status:401});try{let{id:r}=await t,n=await e.json(),s=l.parse(n),a=await i.z.user.findUnique({where:{id:r},include:{studentProfile:!0}});if(!a||"STUDENT"!==a.role)return u.NextResponse.json({error:"Student not found"},{status:404});let o={};s.name&&(o.name=s.name),s.phone&&(o.phone=s.phone),s.email&&(o.email=s.email);let d={};s.level&&(d.level=s.level),s.branch&&(d.branch=s.branch),s.emergencyContact&&(d.emergencyContact=s.emergencyContact),s.status&&(d.status=s.status),s.currentGroupReferenceId&&(d.currentGroupReferenceId=s.currentGroupReferenceId);let c=await i.z.user.update({where:{id:r},data:{...o,updatedAt:new Date,studentProfile:a.studentProfile?{update:{...d,updatedAt:new Date}}:void 0},include:{studentProfile:{include:{currentGroupReference:!0}}}}),p={id:c.id,name:c.name,phone:c.phone,email:c.email,level:c.studentProfile?.level,branch:c.studentProfile?.branch,emergencyContact:c.studentProfile?.emergencyContact,status:c.studentProfile?.status,currentGroup:c.studentProfile?.currentGroupReference,updatedAt:c.updatedAt};return u.NextResponse.json(p)}catch(e){if(e instanceof d.z.ZodError)return u.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Error updating student:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}async function m(e,{params:t}){if(!c(e))return u.NextResponse.json({error:"Unauthorized"},{status:401});try{let{id:e}=await t,r=await i.z.user.findUnique({where:{id:e},include:{studentProfile:{include:{currentGroupReference:{include:{teacherReference:!0}},payments:{orderBy:{createdAt:"desc"},take:10},attendances:{orderBy:{createdAt:"desc"},take:20},assessments:{orderBy:{createdAt:"desc"},take:10}}}}});if(!r||"STUDENT"!==r.role)return u.NextResponse.json({error:"Student not found"},{status:404});let n={id:r.id,name:r.name,phone:r.phone,email:r.email,level:r.studentProfile?.level,branch:r.studentProfile?.branch,emergencyContact:r.studentProfile?.emergencyContact,status:r.studentProfile?.status,currentGroup:r.studentProfile?.currentGroupReference,recentPayments:r.studentProfile?.payments,recentAttendance:r.studentProfile?.attendances,recentAssessments:r.studentProfile?.assessments,createdAt:r.createdAt,updatedAt:r.updatedAt};return u.NextResponse.json({student:n,syncTimestamp:new Date().toISOString()})}catch(e){return console.error("Error syncing student:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}let h=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/inter-server/students/[id]/route",pathname:"/api/inter-server/students/[id]",filename:"route",bundlePath:"app/api/inter-server/students/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\inter-server\\students\\[id]\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:P,workUnitAsyncStorage:y,serverHooks:A}=h;function g(){return(0,o.patchFetch)({workAsyncStorage:P,workUnitAsyncStorage:y})}},43621:(e,t,r)=>{"use strict";r.d(t,{z:()=>s});let n=require("@prisma/client"),s=globalThis.prisma??new n.PrismaClient},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[243,580,697],()=>r(35144));module.exports=n})();